# FLUX QLoRA Fine-tuning on RunPod A40

This guide provides specific instructions for running FLUX QLoRA fine-tuning on RunPod A40 cloud GPUs.

## 🚀 RunPod Quick Start

### 1. Launch RunPod Instance

1. **Go to [RunPod.io](https://runpod.io)**
2. **Select GPU Pod**: Choose A40 (48GB VRAM) - recommended
3. **Template**: Use PyTorch template or Ubuntu with CUDA
4. **Storage**: Allocate at least 100GB for models and data
5. **Environment Variables** (set these in RunPod):
   ```
   HF_TOKEN=your_huggingface_token
   WANDB_API_KEY=your_wandb_api_key
   ```

### 2. Initial Setup

Once your pod is running, connect via SSH or Jupyter and run:

```bash
# Clone the repository
git clone <your-repo-url>
cd qlora

# RunPod-specific setup
make setup-runpod
```

This will:
- Install optimized packages for A40
- Setup workspace directories
- Configure performance optimizations
- Authenticate with <PERSON>ggingFace and W&B

### 3. Upload Training Data

Upload your training images to the pod:

```bash
# Option 1: Using RunPod file manager
# Upload images via the web interface to /workspace/data/train/

# Option 2: Using rsync/scp
rsync -av your_local_images/ runpod_ip:/workspace/data/train/

# Option 3: Download from cloud storage
wget -P /workspace/data/train/ your_dataset_url
```

### 4. Prepare Dataset

```bash
# Auto-caption your images
make prepare

# Or with custom settings
python scripts/prepare_dataset.py \
    --data_dir /workspace/data/train \
    --output_metadata /workspace/data/train_metadata.csv
```

### 5. Start Training

```bash
# Start training optimized for RunPod A40
make train-runpod

# Or manually
python scripts/train.py \
    --config config/training_config.yaml \
    --train_data_dir /workspace/data/train \
    --output_dir /workspace/outputs
```

## ⚙️ RunPod-Specific Optimizations

### A40 GPU Configuration

The RunPod A40 configuration includes:

- **Batch Size**: 2 (vs 1 on smaller GPUs)
- **Gradient Accumulation**: 4 steps
- **Memory Usage**: ~40-42GB VRAM
- **Training Speed**: ~1.5-2 seconds per step
- **Workers**: 8 (optimized for A40's CPU)

### Performance Optimizations

```bash
# Apply performance optimizations
make runpod-optimize

# Monitor GPU usage
make runpod-monitor

# Check system status
make runpod-status
```

### Persistent Storage

Checkpoints are automatically saved to persistent storage:

```bash
# Sync checkpoints to network storage
make runpod-sync

# Create backup
make runpod-backup
```

## 📊 Monitoring on RunPod

### Weights & Biases Integration

Training metrics are automatically logged to W&B:
- Loss curves
- Learning rate schedules
- GPU memory usage
- Training speed
- Generated samples

### Local Monitoring

```bash
# Real-time GPU monitoring
nvidia-smi -l 1

# Training logs
tail -f /workspace/logs/training.log

# System resources
htop
```

## 🔧 RunPod-Specific Commands

### System Management

```bash
make runpod-status      # Check pod status and resources
make runpod-optimize    # Apply performance optimizations
make runpod-monitor     # Real-time GPU monitoring
make runpod-sync        # Sync to persistent storage
make runpod-backup      # Create backup
```

### Training Management

```bash
make train-runpod       # Start training with RunPod config
make resume             # Resume from latest checkpoint
make runpod-quickstart  # Complete setup and start guide
```

## 💰 Cost Optimization

### Efficient Usage Tips

1. **Use Spot Instances**: Save 50-70% on costs
2. **Auto-shutdown**: Stop pod when training completes
3. **Checkpoint Frequently**: Save progress every 250 steps
4. **Monitor Usage**: Use W&B to track training efficiency

### Estimated Costs (A40)

- **On-demand**: ~$0.79/hour
- **Spot**: ~$0.24-0.40/hour
- **Training Time**: 2-6 hours (depending on dataset size)
- **Total Cost**: $5-25 for typical fine-tuning

## 🐛 RunPod Troubleshooting

### Common Issues

**Out of Memory**:
```bash
# Reduce batch size in config
per_device_train_batch_size: 1
gradient_accumulation_steps: 8
```

**Connection Lost**:
```bash
# Training continues in background
# Reconnect and check status
make runpod-status
```

**Slow Download**:
```bash
# Use RunPod's fast storage
# Pre-download models to /workspace/models/
```

**Authentication Issues**:
```bash
# Re-authenticate
huggingface-cli login
wandb login
```

### Performance Issues

**Slow Training**:
```bash
# Apply optimizations
make runpod-optimize

# Check GPU utilization
nvidia-smi

# Increase workers if CPU usage is low
dataloader_num_workers: 12
```

**Memory Leaks**:
```bash
# Restart training with cleanup
python -c "import torch; torch.cuda.empty_cache()"
make train-runpod
```

## 📁 RunPod File Structure

```
/workspace/
├── data/
│   ├── train/              # Your training images
│   └── validation/         # Validation images
├── outputs/                # Training outputs
│   ├── checkpoint-*/       # Model checkpoints
│   └── final_model/        # Final trained model
├── logs/                   # Training logs
├── models/                 # Downloaded models cache
├── network_storage/        # Persistent storage sync
└── generated_images/       # Generated samples
```

## 🔄 Workflow Example

Complete workflow for RunPod A40:

```bash
# 1. Setup (5 minutes)
make setup-runpod

# 2. Upload data (varies)
# Upload via web interface or rsync

# 3. Prepare dataset (10-30 minutes)
make prepare

# 4. Start training (2-6 hours)
make train-runpod

# 5. Generate images (1 minute)
python scripts/inference.py \
    --model_path /workspace/outputs/final_model \
    --prompt "your custom style prompt" \
    --output /workspace/generated_images/result.png

# 6. Backup and cleanup
make runpod-backup
make runpod-sync
```

## 🎯 Best Practices for RunPod

1. **Set Environment Variables**: Configure HF_TOKEN and WANDB_API_KEY
2. **Use Persistent Storage**: Enable network volumes for important data
3. **Monitor Costs**: Track usage in RunPod dashboard
4. **Backup Regularly**: Sync checkpoints to external storage
5. **Optimize Batch Size**: Use batch size 2 for A40
6. **Enable Auto-Resume**: Training continues after disconnections
7. **Use Spot Instances**: Significant cost savings
8. **Pre-download Models**: Cache models to avoid repeated downloads

## 📞 Support

For RunPod-specific issues:
- Check RunPod documentation
- Use RunPod community Discord
- Monitor pod logs in RunPod dashboard

For FLUX training issues:
- Check training logs: `/workspace/logs/training.log`
- Monitor W&B dashboard
- Use validation commands: `make validate`

---

**Happy training on RunPod! 🚀**
