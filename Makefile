# FLUX QLoRA Fine-tuning Makefile

.PHONY: help setup install validate clean train inference test

# Default target
help:
	@echo "FLUX QLoRA Fine-tuning Commands:"
	@echo ""
	@echo "Setup Commands:"
	@echo "  make setup      - Complete setup (install + validate)"
	@echo "  make install    - Install dependencies"
	@echo "  make validate   - Validate installation"
	@echo ""
	@echo "Training Commands:"
	@echo "  make prepare    - Prepare dataset with auto-captioning"
	@echo "  make train      - Start training with default config"
	@echo "  make resume     - Resume training from latest checkpoint"
	@echo ""
	@echo "Inference Commands:"
	@echo "  make inference  - Generate single image"
	@echo "  make batch      - Generate batch of images"
	@echo ""
	@echo "Utility Commands:"
	@echo "  make clean      - Clean generated files"
	@echo "  make test       - Run tests"
	@echo "  make monitor    - Open monitoring dashboard"

# Setup and installation
setup: install validate
	@echo "✅ Setup completed!"

install:
	@echo "📦 Installing dependencies..."
	python setup.py

validate:
	@echo "🔍 Validating setup..."
	python scripts/validate_setup.py

# Dataset preparation
prepare:
	@echo "📸 Preparing dataset..."
	python scripts/prepare_dataset.py \
		--data_dir data/train \
		--output_metadata data/train_metadata.csv

prepare-force:
	@echo "📸 Preparing dataset (force re-captioning)..."
	python scripts/prepare_dataset.py \
		--data_dir data/train \
		--output_metadata data/train_metadata.csv \
		--force_recaption

# Training
train:
	@echo "🚀 Starting training..."
	python scripts/train.py \
		--config config/training_config.yaml

train-debug:
	@echo "🐛 Starting training in debug mode..."
	python scripts/train.py \
		--config config/training_config.yaml \
		--log_level DEBUG

train-dry:
	@echo "🧪 Dry run training..."
	python scripts/train.py \
		--config config/training_config.yaml \
		--dry_run

resume:
	@echo "🔄 Resuming training..."
	@latest_checkpoint=$$(find outputs -name "checkpoint-*" -type d | sort -V | tail -1); \
	if [ -n "$$latest_checkpoint" ]; then \
		echo "Resuming from: $$latest_checkpoint"; \
		python scripts/train.py \
			--config config/training_config.yaml \
			--resume_from "$$latest_checkpoint"; \
	else \
		echo "No checkpoints found. Starting fresh training."; \
		make train; \
	fi

# Inference
inference:
	@echo "🎨 Generating image..."
	@read -p "Enter prompt: " prompt; \
	python scripts/inference.py \
		--model_path outputs/final_model \
		--prompt "$$prompt" \
		--output generated_image.png

inference-batch:
	@echo "🎨 Batch image generation..."
	python scripts/inference.py \
		--model_path outputs/final_model \
		--batch_prompts prompts.txt \
		--batch_output_dir generated_images/

# Monitoring
monitor:
	@echo "📊 Opening monitoring dashboard..."
	@if command -v wandb >/dev/null 2>&1; then \
		wandb dashboard; \
	else \
		echo "W&B not installed. Install with: pip install wandb"; \
	fi

# Testing
test:
	@echo "🧪 Running tests..."
	python -m pytest tests/ -v

test-gpu:
	@echo "🖥️ Testing GPU functionality..."
	python -c "import torch; print(f'CUDA available: {torch.cuda.is_available()}'); print(f'GPU count: {torch.cuda.device_count()}'); print(f'Current device: {torch.cuda.current_device() if torch.cuda.is_available() else \"None\"}')"

# Utility commands
clean:
	@echo "🧹 Cleaning generated files..."
	rm -rf outputs/*/
	rm -rf logs/*.log
	rm -rf generated_images/*.png
	rm -rf __pycache__/
	rm -rf src/__pycache__/
	find . -name "*.pyc" -delete
	find . -name "*.pyo" -delete

clean-all: clean
	@echo "🧹 Deep cleaning..."
	rm -rf models/
	rm -rf data/train_metadata.csv
	rm -rf .wandb/

# Development commands
format:
	@echo "🎨 Formatting code..."
	black src/ scripts/
	isort src/ scripts/

lint:
	@echo "🔍 Linting code..."
	flake8 src/ scripts/
	pylint src/ scripts/

# Docker commands (if using Docker)
docker-build:
	@echo "🐳 Building Docker image..."
	docker build -t flux-qlora .

docker-run:
	@echo "🐳 Running in Docker..."
	docker run --gpus all -v $(PWD):/workspace flux-qlora

# Quick start for new users
quickstart:
	@echo "🚀 Quick start setup..."
	@echo "1. Setting up environment..."
	make setup
	@echo ""
	@echo "2. Please add your training images to data/train/"
	@echo "3. Then run: make prepare"
	@echo "4. Finally run: make train"
	@echo ""
	@echo "For detailed instructions, see README.md"

# Memory optimization presets
train-a40:
	@echo "🚀 Training optimized for A40 GPU..."
	python scripts/train.py \
		--config config/training_config.yaml

train-rtx3090:
	@echo "🚀 Training optimized for RTX 3090..."
	python scripts/train.py \
		--config config/training_config_rtx3090.yaml

train-minimal:
	@echo "🚀 Minimal memory training..."
	python scripts/train.py \
		--config config/training_config_minimal.yaml

# Backup and restore
backup:
	@echo "💾 Creating backup..."
	@timestamp=$$(date +%Y%m%d_%H%M%S); \
	tar -czf "backup_$$timestamp.tar.gz" \
		config/ src/ scripts/ data/ outputs/ \
		--exclude="outputs/*/pytorch_model.bin" \
		--exclude="data/train/*.jpg" \
		--exclude="data/train/*.png"
	@echo "Backup created: backup_$$timestamp.tar.gz"

# System info
info:
	@echo "ℹ️ System Information:"
	@echo "Python: $$(python --version)"
	@echo "PyTorch: $$(python -c 'import torch; print(torch.__version__)')"
	@echo "CUDA: $$(python -c 'import torch; print(torch.version.cuda if torch.cuda.is_available() else \"Not available\")')"
	@echo "GPU: $$(python -c 'import torch; print(torch.cuda.get_device_name(0) if torch.cuda.is_available() else \"Not available\")')"
	@echo "GPU Memory: $$(python -c 'import torch; print(f\"{torch.cuda.get_device_properties(0).total_memory/1024**3:.1f}GB\" if torch.cuda.is_available() else \"Not available\")')"
