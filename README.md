# FLUX QLoRA Fine-tuning Project

A complete implementation for fine-tuning the FLUX.1-dev text-to-image model using QLoRA (Quantized Low-Rank Adaptation) with advanced auto-captioning capabilities. Optimized for A40 GPU and supports variable image sizes.

## 🚀 Features

- **QLoRA Fine-tuning**: Memory-efficient training using 4-bit quantization and LoRA
- **Advanced Auto-captioning**: Multiple captioning models (BLIP2, CLIP Interrogator, CogVLM)
- **Variable Image Sizes**: Smart batching and processing for images of different resolutions
- **Memory Optimization**: Designed for A40 GPU (48GB VRAM) with gradient checkpointing
- **Real-time Monitoring**: Weights & Biases integration and rich console output
- **Robust Data Handling**: Automatic dataset validation and preprocessing

## 📋 Requirements

### Hardware
- **Recommended**: NVIDIA A40 (48GB VRAM) or similar
- **Minimum**: RTX 3090/4090 (24GB VRAM) with reduced batch size
- **RAM**: 32GB+ system memory
- **Storage**: 100GB+ free space

### Software
- Python 3.8+
- CUDA 11.8+ or 12.0+
- PyTorch 2.1+

## 🛠️ Installation

1. **Clone the repository**:
```bash
git clone <repository-url>
cd qlora
```

2. **Create virtual environment**:
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. **Install dependencies**:
```bash
pip install -r requirements.txt
```

4. **Install additional captioning models** (optional):
```bash
# For CogVLM support
pip install git+https://github.com/THUDM/CogVLM.git

# For LLaVA support
pip install git+https://github.com/haotian-liu/LLaVA.git
```

## 📁 Project Structure

```
qlora/
├── config/
│   └── training_config.yaml      # Training configuration
├── src/
│   ├── dataset_processor.py      # Auto-captioning and dataset processing
│   ├── data_loader.py            # Custom data loader for variable sizes
│   ├── flux_qlora_trainer.py     # Main training implementation
│   └── utils.py                  # Utility functions
├── scripts/
│   ├── prepare_dataset.py        # Dataset preparation script
│   ├── train.py                  # Training launcher
│   └── inference.py              # Inference script
├── data/
│   ├── images/                   # Input: ALL your images go here
│   ├── train/                    # Output: Auto-generated training set
│   └── validation/               # Output: Auto-generated validation set
├── outputs/                      # Training outputs and checkpoints
├── logs/                         # Training logs
└── requirements.txt              # Dependencies
```

## 🚀 Quick Start

### 1. Prepare Your Dataset

Place ALL your images in a single directory:

```bash
mkdir -p data/images
# Copy ALL your images to data/images/ (no need to separate train/val)
```

### 2. Auto-caption and Split Dataset

```bash
python scripts/prepare_dataset.py
```

This will:
- Automatically generate captions using BLIP2 and CLIP Interrogator
- Automatically split into train (90%) and validation (10%) sets
- Create symlinks to save disk space
- Validate image formats and sizes
- Create metadata for training

The script creates:
- `data/train/` - Training images (90%)
- `data/validation/` - Validation images (10%)
- Captions as `.txt` files alongside each image

### 3. Configure Training

Edit `config/training_config.yaml` to adjust:
- Model parameters (LoRA rank, learning rate, etc.)
- Image processing settings
- Training hyperparameters
- Hardware optimization settings

### 4. Start Training

```bash
python scripts/train.py \
    --config config/training_config.yaml \
    --train_data_dir data/train
```

### 5. Generate Images

After training, use your fine-tuned model:

```bash
python scripts/inference.py \
    --model_path outputs/checkpoint-epoch-10-step-1000 \
    --prompt "your custom prompt here" \
    --output generated_image.png
```

## ⚙️ Configuration

### Key Configuration Options

**Model Settings**:
```yaml
model:
  name: "black-forest-labs/FLUX.1-dev"
  torch_dtype: "bfloat16"

qlora:
  r: 64                    # LoRA rank (higher = more parameters)
  lora_alpha: 128          # LoRA scaling factor
  lora_dropout: 0.1        # Dropout for LoRA layers
```

**Training Settings**:
```yaml
training:
  num_train_epochs: 10
  per_device_train_batch_size: 1
  gradient_accumulation_steps: 8
  learning_rate: 1e-4
```

**Image Processing**:
```yaml
image:
  resolution: 1024         # Target resolution
  min_resolution: 512      # Minimum allowed resolution
  max_resolution: 1536     # Maximum allowed resolution
```

**Auto-captioning**:
```yaml
captioning:
  enabled: true
  models: ["blip2", "clip_interrogator"]
  combine_captions: true
```

## 📊 Memory Usage

### Estimated VRAM Requirements

| Configuration | VRAM Usage | Batch Size | Notes |
|---------------|------------|------------|-------|
| Default (A40) | ~35-40GB | 1 | Recommended for A40 |
| Reduced | ~20-25GB | 1 | For RTX 3090/4090 |
| Minimal | ~15-18GB | 1 | Reduced LoRA rank |

### Memory Optimization Tips

1. **Reduce batch size**: Set `per_device_train_batch_size: 1`
2. **Increase gradient accumulation**: Use `gradient_accumulation_steps: 16`
3. **Lower LoRA rank**: Set `r: 32` instead of `r: 64`
4. **Enable CPU offloading**: Set `use_cpu_offload: true`

## 🔧 Advanced Usage

### Custom Captioning Models

Add your own captioning model:

```python
# In src/dataset_processor.py
def _load_custom_model(self):
    # Your custom model loading code
    pass
```

### Multi-GPU Training

The trainer supports multi-GPU training via Accelerate:

```bash
accelerate config  # Configure multi-GPU setup
accelerate launch scripts/train.py --config config/training_config.yaml
```

### Resuming Training

```bash
python scripts/train.py \
    --config config/training_config.yaml \
    --resume_from outputs/checkpoint-epoch-5-step-500
```

### Batch Inference

Generate multiple images from a text file:

```bash
python scripts/inference.py \
    --model_path outputs/final_model \
    --batch_prompts prompts.txt \
    --batch_output_dir generated_images/
```

## 📈 Monitoring

### Weights & Biases

Enable W&B logging in config:

```yaml
monitoring:
  use_wandb: true
  project_name: "flux-qlora-finetuning"
```

### Local Monitoring

Training progress is displayed in the console with:
- Real-time loss tracking
- Memory usage monitoring
- Training speed metrics
- Validation scores

## 🐛 Troubleshooting

### Common Issues

**Out of Memory**:
- Reduce batch size to 1
- Increase gradient accumulation steps
- Lower image resolution
- Reduce LoRA rank

**Slow Training**:
- Enable gradient checkpointing
- Use mixed precision (bf16)
- Optimize data loading (increase num_workers)

**Poor Results**:
- Increase LoRA rank
- Adjust learning rate
- Improve caption quality
- Train for more epochs

### Debug Mode

Run with debug logging:

```bash
python scripts/train.py --log_level DEBUG
```

## 📝 Tips for Best Results

1. **High-quality captions**: Detailed, descriptive captions improve results
2. **Consistent style**: Train on images with similar style/domain
3. **Sufficient data**: 100+ images minimum, 500+ recommended
4. **Proper resolution**: Use images close to target resolution
5. **Validation set**: Monitor overfitting with validation data

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- [Black Forest Labs](https://blackforestlabs.ai/) for FLUX.1-dev
- [Hugging Face](https://huggingface.co/) for transformers and diffusers
- [Microsoft](https://github.com/microsoft/LoRA) for LoRA implementation
- [Tim Dettmers](https://github.com/TimDettmers/bitsandbytes) for bitsandbytes

## 📞 Support

For issues and questions:
- Open an issue on GitHub
- Check the troubleshooting section
- Review the configuration documentation

---

**Happy fine-tuning! 🎨✨**
