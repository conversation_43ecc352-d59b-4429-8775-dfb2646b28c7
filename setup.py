#!/usr/bin/env python3
"""
Setup script for FLUX QLoRA fine-tuning project.
"""

import os
import subprocess
import sys
from pathlib import Path

def run_command(command, description):
    """Run a command and handle errors."""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed:")
        print(f"Error: {e.stderr}")
        return False

def check_gpu():
    """Check GPU availability and CUDA version."""
    try:
        import torch
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            gpu_name = torch.cuda.get_device_name(0)
            cuda_version = torch.version.cuda
            print(f"✅ GPU detected: {gpu_name}")
            print(f"✅ CUDA version: {cuda_version}")
            print(f"✅ GPU count: {gpu_count}")
            
            # Check VRAM
            gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
            print(f"✅ GPU memory: {gpu_memory:.1f} GB")
            
            if gpu_memory < 20:
                print("⚠️  Warning: GPU has less than 20GB VRAM. Consider reducing batch size.")
            
            return True
        else:
            print("❌ No GPU detected. Training will be very slow on CPU.")
            return False
    except ImportError:
        print("❌ PyTorch not installed. Cannot check GPU.")
        return False

def create_directories():
    """Create necessary directories."""
    directories = [
        "data/train",
        "data/validation",
        "outputs",
        "logs",
        "models",
        "checkpoints",
        "generated_images"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"📁 Created directory: {directory}")

def setup_git_lfs():
    """Setup Git LFS for large files."""
    if not run_command("git lfs version", "Checking Git LFS"):
        print("⚠️  Git LFS not found. Large model files may not be handled properly.")
        return False
    
    # Setup LFS tracking for model files
    lfs_patterns = [
        "*.bin",
        "*.safetensors", 
        "*.ckpt",
        "*.pth",
        "*.pt"
    ]
    
    for pattern in lfs_patterns:
        run_command(f"git lfs track '{pattern}'", f"Setting up LFS tracking for {pattern}")
    
    return True

def install_dependencies():
    """Install Python dependencies."""
    print("📦 Installing Python dependencies...")
    
    # Upgrade pip first
    if not run_command(f"{sys.executable} -m pip install --upgrade pip", "Upgrading pip"):
        return False
    
    # Install main requirements
    if not run_command(f"{sys.executable} -m pip install -r requirements.txt", "Installing requirements"):
        return False
    
    # Install additional packages for better performance
    additional_packages = [
        "flash-attn --no-build-isolation",  # For faster attention
        "xformers",  # Memory efficient transformers
    ]
    
    for package in additional_packages:
        run_command(f"{sys.executable} -m pip install {package}", f"Installing {package.split()[0]}")
    
    return True

def setup_wandb():
    """Setup Weights & Biases."""
    try:
        import wandb
        print("🔑 Setting up Weights & Biases...")
        print("Please run 'wandb login' to authenticate with your W&B account.")
        print("You can create a free account at https://wandb.ai/")
        return True
    except ImportError:
        print("❌ wandb not installed. Monitoring features will be limited.")
        return False

def validate_installation():
    """Validate the installation."""
    print("\n🔍 Validating installation...")
    
    required_packages = [
        "torch",
        "transformers", 
        "diffusers",
        "peft",
        "bitsandbytes",
        "accelerate"
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package}")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n❌ Missing packages: {', '.join(missing_packages)}")
        return False
    
    print("\n✅ All required packages installed successfully!")
    return True

def main():
    """Main setup function."""
    print("🚀 FLUX QLoRA Fine-tuning Setup")
    print("=" * 50)
    
    # Create directories
    print("\n📁 Creating directory structure...")
    create_directories()
    
    # Install dependencies
    print("\n📦 Installing dependencies...")
    if not install_dependencies():
        print("❌ Failed to install dependencies")
        return 1
    
    # Check GPU
    print("\n🖥️  Checking GPU availability...")
    check_gpu()
    
    # Setup Git LFS
    print("\n📚 Setting up Git LFS...")
    setup_git_lfs()
    
    # Setup W&B
    print("\n📊 Setting up monitoring...")
    setup_wandb()
    
    # Validate installation
    if not validate_installation():
        print("❌ Installation validation failed")
        return 1
    
    print("\n🎉 Setup completed successfully!")
    print("\nNext steps:")
    print("1. Place your training images in data/train/")
    print("2. Run: python scripts/prepare_dataset.py --data_dir data/train")
    print("3. Run: python scripts/train.py")
    print("\nFor detailed instructions, see README.md")
    
    return 0

if __name__ == "__main__":
    exit(main())
