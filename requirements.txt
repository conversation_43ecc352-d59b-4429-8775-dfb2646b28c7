# Core ML libraries
torch>=2.1.0
torchvision>=0.16.0
transformers>=4.36.0
diffusers>=0.24.0
accelerate>=0.25.0
datasets>=2.14.0

# QLoRA and PEFT
peft>=0.7.0
bitsandbytes>=0.41.0

# Image processing and captioning
Pillow>=10.0.0
opencv-python>=4.8.0
clip-interrogator>=0.6.0
salesforce-blip>=1.0.0

# Additional captioning models
transformers[torch]
sentence-transformers>=2.2.0

# Data handling
pandas>=2.0.0
numpy>=1.24.0
tqdm>=4.65.0
wandb>=0.16.0

# Configuration and utilities
omegaconf>=2.3.0
hydra-core>=1.3.0
rich>=13.0.0
typer>=0.9.0

# Development tools
jupyter>=1.0.0
matplotlib>=3.7.0
seaborn>=0.12.0

# Optional: For advanced captioning
# git+https://github.com/THUDM/CogVLM.git
# git+https://github.com/haotian-liu/LLaVA.git
