"""
FLUX QLoRA Trainer - Main training implementation for FLUX.1-dev fine-tuning.
Optimized for A40 GPU with memory-efficient training.
"""

import torch
import torch.nn as nn
from torch.utils.data import DataLoader
from transformers import (
    AutoTokenizer, 
    TrainingArguments, 
    Trainer,
    get_linear_schedule_with_warmup
)
from diffusers import FluxPipeline, FluxTransformer2DModel
from peft import LoraConfig, get_peft_model, TaskType
import bitsandbytes as bnb
from accelerate import Accelerator
import wandb
import os
import logging
from typing import Dict, List, Optional
import numpy as np
from tqdm import tqdm
import gc

from .data_loader import create_dataloader
from .utils import save_checkpoint, load_checkpoint, calculate_memory_usage

logger = logging.getLogger(__name__)


class FluxQLoRATrainer:
    """QLoRA trainer for FLUX.1-dev model."""
    
    def __init__(self, config: Dict):
        self.config = config
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        # Initialize accelerator for distributed training
        self.accelerator = Accelerator(
            mixed_precision=config.get('hardware', {}).get('mixed_precision', 'bf16'),
            gradient_accumulation_steps=config.get('training', {}).get('gradient_accumulation_steps', 8)
        )
        
        # Initialize wandb if enabled
        if config.get('monitoring', {}).get('use_wandb', False):
            wandb.init(
                project=config.get('monitoring', {}).get('project_name', 'flux-qlora'),
                name=config.get('monitoring', {}).get('run_name'),
                config=config
            )
        
        self.model = None
        self.tokenizer = None
        self.optimizer = None
        self.scheduler = None
        
    def setup_model(self):
        """Setup FLUX model with QLoRA configuration."""
        logger.info("Loading FLUX model...")
        
        model_name = self.config['model']['name']
        cache_dir = self.config['model'].get('cache_dir', './models')
        
        # Load tokenizer
        self.tokenizer = AutoTokenizer.from_pretrained(
            "openai/clip-vit-large-patch14",
            cache_dir=cache_dir
        )
        
        # Quantization config
        quantization_config = self.config.get('quantization', {})
        bnb_config = None
        
        if quantization_config.get('load_in_4bit', False):
            from transformers import BitsAndBytesConfig
            bnb_config = BitsAndBytesConfig(
                load_in_4bit=True,
                bnb_4bit_compute_dtype=getattr(torch, quantization_config.get('bnb_4bit_compute_dtype', 'bfloat16')),
                bnb_4bit_use_double_quant=quantization_config.get('bnb_4bit_use_double_quant', True),
                bnb_4bit_quant_type=quantization_config.get('bnb_4bit_quant_type', 'nf4')
            )
        
        # Load FLUX pipeline
        self.pipeline = FluxPipeline.from_pretrained(
            model_name,
            torch_dtype=getattr(torch, self.config['model'].get('torch_dtype', 'bfloat16')),
            quantization_config=bnb_config,
            cache_dir=cache_dir,
            device_map="auto"
        )
        
        # Extract the transformer model for training
        self.model = self.pipeline.transformer
        
        # Setup LoRA
        self._setup_lora()
        
        # Enable gradient checkpointing if specified
        if self.config.get('hardware', {}).get('gradient_checkpointing', True):
            self.model.gradient_checkpointing_enable()
        
        logger.info(f"Model loaded. Memory usage: {calculate_memory_usage():.2f} GB")
    
    def _setup_lora(self):
        """Setup LoRA configuration for the model."""
        qlora_config = self.config.get('qlora', {})
        
        # Define LoRA config
        lora_config = LoraConfig(
            r=qlora_config.get('r', 64),
            lora_alpha=qlora_config.get('lora_alpha', 128),
            target_modules=qlora_config.get('target_modules', [
                "to_q", "to_k", "to_v", "to_out.0", "proj_in", "proj_out"
            ]),
            lora_dropout=qlora_config.get('lora_dropout', 0.1),
            bias=qlora_config.get('bias', 'none'),
            task_type=TaskType.FEATURE_EXTRACTION
        )
        
        # Apply LoRA to model
        self.model = get_peft_model(self.model, lora_config)
        
        # Print trainable parameters
        trainable_params = sum(p.numel() for p in self.model.parameters() if p.requires_grad)
        total_params = sum(p.numel() for p in self.model.parameters())
        
        logger.info(f"Trainable parameters: {trainable_params:,}")
        logger.info(f"Total parameters: {total_params:,}")
        logger.info(f"Trainable ratio: {100 * trainable_params / total_params:.2f}%")
    
    def setup_optimizer_and_scheduler(self, num_training_steps: int):
        """Setup optimizer and learning rate scheduler."""
        training_config = self.config.get('training', {})
        
        # Use AdamW with 8-bit optimization
        self.optimizer = bnb.optim.AdamW8bit(
            self.model.parameters(),
            lr=training_config.get('learning_rate', 1e-4),
            weight_decay=training_config.get('weight_decay', 0.01),
            betas=(0.9, 0.999),
            eps=1e-8
        )
        
        # Setup scheduler
        warmup_steps = training_config.get('warmup_steps', 100)
        self.scheduler = get_linear_schedule_with_warmup(
            self.optimizer,
            num_warmup_steps=warmup_steps,
            num_training_steps=num_training_steps
        )
    
    def compute_loss(self, batch):
        """Compute the diffusion loss for the batch."""
        pixel_values = batch["pixel_values"].to(self.device)
        input_ids = batch["input_ids"].to(self.device)
        
        # Encode text
        text_embeddings = self.pipeline.text_encoder(input_ids)[0]
        
        # Add noise to images (diffusion forward process)
        noise = torch.randn_like(pixel_values)
        timesteps = torch.randint(
            0, self.pipeline.scheduler.config.num_train_timesteps,
            (pixel_values.shape[0],), device=self.device
        ).long()
        
        # Forward diffusion process
        noisy_images = self.pipeline.scheduler.add_noise(pixel_values, noise, timesteps)
        
        # Predict noise
        model_pred = self.model(
            noisy_images,
            timesteps,
            encoder_hidden_states=text_embeddings,
            return_dict=False
        )[0]
        
        # Compute loss
        if self.pipeline.scheduler.config.prediction_type == "epsilon":
            target = noise
        elif self.pipeline.scheduler.config.prediction_type == "v_prediction":
            target = self.pipeline.scheduler.get_velocity(pixel_values, noise, timesteps)
        else:
            raise ValueError(f"Unknown prediction type {self.pipeline.scheduler.config.prediction_type}")
        
        loss = nn.functional.mse_loss(model_pred.float(), target.float(), reduction="mean")
        return loss
    
    def train_epoch(self, dataloader: DataLoader, epoch: int):
        """Train for one epoch."""
        self.model.train()
        total_loss = 0
        num_batches = len(dataloader)
        
        progress_bar = tqdm(dataloader, desc=f"Epoch {epoch}")
        
        for step, batch in enumerate(progress_bar):
            with self.accelerator.accumulate(self.model):
                # Compute loss
                loss = self.compute_loss(batch)
                
                # Backward pass
                self.accelerator.backward(loss)
                
                # Gradient clipping
                max_grad_norm = self.config.get('training', {}).get('max_grad_norm', 1.0)
                if max_grad_norm > 0:
                    self.accelerator.clip_grad_norm_(self.model.parameters(), max_grad_norm)
                
                # Optimizer step
                self.optimizer.step()
                self.scheduler.step()
                self.optimizer.zero_grad()
            
            # Update metrics
            total_loss += loss.item()
            avg_loss = total_loss / (step + 1)
            
            # Update progress bar
            progress_bar.set_postfix({
                'loss': f'{loss.item():.4f}',
                'avg_loss': f'{avg_loss:.4f}',
                'lr': f'{self.scheduler.get_last_lr()[0]:.2e}',
                'memory': f'{calculate_memory_usage():.1f}GB'
            })
            
            # Log to wandb
            if self.config.get('monitoring', {}).get('use_wandb', False):
                wandb.log({
                    'train_loss': loss.item(),
                    'learning_rate': self.scheduler.get_last_lr()[0],
                    'epoch': epoch,
                    'step': step,
                    'memory_usage_gb': calculate_memory_usage()
                })
            
            # Save checkpoint
            save_steps = self.config.get('training', {}).get('save_steps', 500)
            if (step + 1) % save_steps == 0:
                self.save_checkpoint(epoch, step)
            
            # Clear cache periodically
            if (step + 1) % 100 == 0:
                torch.cuda.empty_cache()
                gc.collect()
        
        return total_loss / num_batches
    
    def save_checkpoint(self, epoch: int, step: int):
        """Save model checkpoint."""
        output_dir = self.config.get('training', {}).get('output_dir', './outputs')
        checkpoint_dir = os.path.join(output_dir, f"checkpoint-epoch-{epoch}-step-{step}")
        
        os.makedirs(checkpoint_dir, exist_ok=True)
        
        # Save LoRA weights
        self.model.save_pretrained(checkpoint_dir)
        
        # Save tokenizer
        self.tokenizer.save_pretrained(checkpoint_dir)
        
        # Save training state
        save_checkpoint(
            checkpoint_dir,
            epoch=epoch,
            step=step,
            optimizer_state=self.optimizer.state_dict(),
            scheduler_state=self.scheduler.state_dict(),
            config=self.config
        )
        
        logger.info(f"Checkpoint saved to {checkpoint_dir}")
    
    def train(self, train_dataloader: DataLoader, val_dataloader: Optional[DataLoader] = None):
        """Main training loop."""
        num_epochs = self.config.get('training', {}).get('num_train_epochs', 10)
        num_training_steps = len(train_dataloader) * num_epochs
        
        # Setup optimizer and scheduler
        self.setup_optimizer_and_scheduler(num_training_steps)
        
        # Prepare for distributed training
        self.model, self.optimizer, train_dataloader = self.accelerator.prepare(
            self.model, self.optimizer, train_dataloader
        )
        
        logger.info(f"Starting training for {num_epochs} epochs...")
        logger.info(f"Total training steps: {num_training_steps}")
        
        for epoch in range(num_epochs):
            # Train epoch
            avg_loss = self.train_epoch(train_dataloader, epoch)
            
            logger.info(f"Epoch {epoch} completed. Average loss: {avg_loss:.4f}")
            
            # Validation
            if val_dataloader is not None:
                val_loss = self.validate(val_dataloader)
                logger.info(f"Validation loss: {val_loss:.4f}")
                
                if self.config.get('monitoring', {}).get('use_wandb', False):
                    wandb.log({'val_loss': val_loss, 'epoch': epoch})
            
            # Save epoch checkpoint
            self.save_checkpoint(epoch, len(train_dataloader))
        
        logger.info("Training completed!")
    
    def validate(self, val_dataloader: DataLoader) -> float:
        """Validate the model."""
        self.model.eval()
        total_loss = 0
        num_batches = len(val_dataloader)
        
        with torch.no_grad():
            for batch in tqdm(val_dataloader, desc="Validation"):
                loss = self.compute_loss(batch)
                total_loss += loss.item()
        
        return total_loss / num_batches
