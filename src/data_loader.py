"""
Custom data loader for FLUX fine-tuning with variable image sizes and smart batching.
"""

import torch
from torch.utils.data import Dataset, DataLoader
from PIL import Image
import numpy as np
from typing import List, Dict, Tuple, Optional
import random
from pathlib import Path
import logging

logger = logging.getLogger(__name__)


class FluxDataset(Dataset):
    """Dataset class for FLUX fine-tuning with variable image sizes."""
    
    def __init__(self, dataset_items: List[Dict], config: Dict, tokenizer=None):
        self.dataset_items = dataset_items
        self.config = config
        self.tokenizer = tokenizer
        
        # Image processing parameters
        self.target_resolution = config.get('image', {}).get('resolution', 1024)
        self.min_resolution = config.get('image', {}).get('min_resolution', 512)
        self.max_resolution = config.get('image', {}).get('max_resolution', 1536)
        self.center_crop = config.get('image', {}).get('center_crop', False)
        self.random_flip = config.get('image', {}).get('random_flip', True)
        
        # Text processing parameters
        self.max_sequence_length = config.get('dataset', {}).get('max_sequence_length', 512)
        
        # Group images by similar aspect ratios for efficient batching
        self._group_by_aspect_ratio()
    
    def _group_by_aspect_ratio(self):
        """Group images by aspect ratio for more efficient batching."""
        aspect_ratio_groups = {}
        
        for idx, item in enumerate(self.dataset_items):
            aspect_ratio = item['aspect_ratio']
            # Round to nearest 0.1 for grouping
            rounded_ratio = round(aspect_ratio, 1)
            
            if rounded_ratio not in aspect_ratio_groups:
                aspect_ratio_groups[rounded_ratio] = []
            aspect_ratio_groups[rounded_ratio].append(idx)
        
        self.aspect_ratio_groups = aspect_ratio_groups
        logger.info(f"Grouped images into {len(aspect_ratio_groups)} aspect ratio groups")
    
    def __len__(self):
        return len(self.dataset_items)
    
    def __getitem__(self, idx):
        item = self.dataset_items[idx]
        
        # Load and process image
        image = self._load_and_process_image(item['image_path'])
        
        # Process caption
        caption = item['caption']
        if self.tokenizer:
            text_inputs = self.tokenizer(
                caption,
                max_length=self.max_sequence_length,
                padding="max_length",
                truncation=True,
                return_tensors="pt"
            )
        else:
            text_inputs = {"input_ids": caption}
        
        return {
            "pixel_values": image,
            "input_ids": text_inputs["input_ids"].squeeze(),
            "attention_mask": text_inputs.get("attention_mask", torch.ones_like(text_inputs["input_ids"])).squeeze(),
            "caption": caption,
            "original_size": (item['width'], item['height'])
        }
    
    def _load_and_process_image(self, image_path: str) -> torch.Tensor:
        """Load and process an image with smart resizing."""
        image = Image.open(image_path).convert('RGB')
        original_width, original_height = image.size
        
        # Calculate target size maintaining aspect ratio
        target_size = self._calculate_target_size(original_width, original_height)
        
        # Resize image
        if target_size != (original_width, original_height):
            image = image.resize(target_size, Image.Resampling.LANCZOS)
        
        # Apply random flip if enabled
        if self.random_flip and random.random() > 0.5:
            image = image.transpose(Image.Transpose.FLIP_LEFT_RIGHT)
        
        # Convert to tensor and normalize
        image_array = np.array(image).astype(np.float32) / 255.0
        image_tensor = torch.from_numpy(image_array).permute(2, 0, 1)
        
        # Normalize to [-1, 1] range (standard for diffusion models)
        image_tensor = image_tensor * 2.0 - 1.0
        
        return image_tensor
    
    def _calculate_target_size(self, width: int, height: int) -> Tuple[int, int]:
        """Calculate target size for image resizing."""
        aspect_ratio = width / height
        
        if self.center_crop:
            # Center crop to square
            size = min(width, height)
            target_size = min(max(size, self.min_resolution), self.max_resolution)
            return (target_size, target_size)
        else:
            # Maintain aspect ratio
            if aspect_ratio > 1:  # Landscape
                target_width = min(max(self.target_resolution, self.min_resolution), self.max_resolution)
                target_height = int(target_width / aspect_ratio)
                # Ensure height is within bounds
                if target_height < self.min_resolution:
                    target_height = self.min_resolution
                    target_width = int(target_height * aspect_ratio)
                elif target_height > self.max_resolution:
                    target_height = self.max_resolution
                    target_width = int(target_height * aspect_ratio)
            else:  # Portrait or square
                target_height = min(max(self.target_resolution, self.min_resolution), self.max_resolution)
                target_width = int(target_height * aspect_ratio)
                # Ensure width is within bounds
                if target_width < self.min_resolution:
                    target_width = self.min_resolution
                    target_height = int(target_width / aspect_ratio)
                elif target_width > self.max_resolution:
                    target_width = self.max_resolution
                    target_height = int(target_width / aspect_ratio)
            
            # Ensure dimensions are multiples of 8 (common requirement for diffusion models)
            target_width = (target_width // 8) * 8
            target_height = (target_height // 8) * 8
            
            return (target_width, target_height)


class SmartBatchSampler:
    """Smart batch sampler that groups images with similar sizes."""
    
    def __init__(self, dataset: FluxDataset, batch_size: int, shuffle: bool = True):
        self.dataset = dataset
        self.batch_size = batch_size
        self.shuffle = shuffle
        
    def __iter__(self):
        # Get all indices grouped by aspect ratio
        all_batches = []
        
        for aspect_ratio, indices in self.dataset.aspect_ratio_groups.items():
            if self.shuffle:
                random.shuffle(indices)
            
            # Create batches from this aspect ratio group
            for i in range(0, len(indices), self.batch_size):
                batch = indices[i:i + self.batch_size]
                all_batches.append(batch)
        
        # Shuffle batches if requested
        if self.shuffle:
            random.shuffle(all_batches)
        
        for batch in all_batches:
            yield batch
    
    def __len__(self):
        total_batches = 0
        for indices in self.dataset.aspect_ratio_groups.values():
            total_batches += (len(indices) + self.batch_size - 1) // self.batch_size
        return total_batches


def collate_fn(batch):
    """Custom collate function for variable-sized images."""
    # Find the maximum dimensions in the batch
    max_height = max(item["pixel_values"].shape[1] for item in batch)
    max_width = max(item["pixel_values"].shape[2] for item in batch)
    
    # Pad all images to the same size
    padded_images = []
    for item in batch:
        image = item["pixel_values"]
        h, w = image.shape[1], image.shape[2]
        
        # Calculate padding
        pad_h = max_height - h
        pad_w = max_width - w
        
        # Pad image (pad with zeros, which corresponds to -1 in normalized space)
        padded_image = torch.nn.functional.pad(
            image, 
            (0, pad_w, 0, pad_h), 
            mode='constant', 
            value=-1.0
        )
        padded_images.append(padded_image)
    
    # Stack everything
    pixel_values = torch.stack(padded_images)
    input_ids = torch.stack([item["input_ids"] for item in batch])
    attention_mask = torch.stack([item["attention_mask"] for item in batch])
    
    return {
        "pixel_values": pixel_values,
        "input_ids": input_ids,
        "attention_mask": attention_mask,
        "captions": [item["caption"] for item in batch],
        "original_sizes": [item["original_size"] for item in batch]
    }


def create_dataloader(dataset_items: List[Dict], config: Dict, tokenizer=None, shuffle: bool = True) -> DataLoader:
    """Create a DataLoader with smart batching for variable-sized images."""
    dataset = FluxDataset(dataset_items, config, tokenizer)
    
    batch_size = config.get('training', {}).get('per_device_train_batch_size', 1)
    num_workers = config.get('training', {}).get('dataloader_num_workers', 4)
    pin_memory = config.get('hardware', {}).get('pin_memory', True)
    
    # Use smart batch sampler for better memory efficiency
    batch_sampler = SmartBatchSampler(dataset, batch_size, shuffle)
    
    dataloader = DataLoader(
        dataset,
        batch_sampler=batch_sampler,
        collate_fn=collate_fn,
        num_workers=num_workers,
        pin_memory=pin_memory
    )
    
    return dataloader
