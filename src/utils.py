"""
Utility functions for FLUX QLoRA training.
"""

import torch
import json
import os
import psutil
import <PERSON><PERSON><PERSON>
from typing import Dict, Any, Optional
import logging
from pathlib import Path
import yaml
from omegaconf import OmegaConf

logger = logging.getLogger(__name__)


def load_config(config_path: str) -> Dict[str, Any]:
    """Load configuration from YAML file."""
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    return config


def save_config(config: Dict[str, Any], save_path: str):
    """Save configuration to YAML file."""
    os.makedirs(os.path.dirname(save_path), exist_ok=True)
    with open(save_path, 'w') as f:
        yaml.dump(config, f, default_flow_style=False, indent=2)


def calculate_memory_usage() -> float:
    """Calculate current GPU memory usage in GB."""
    if torch.cuda.is_available():
        return torch.cuda.memory_allocated() / 1024**3
    return 0.0


def get_system_info() -> Dict[str, Any]:
    """Get system information including GPU and memory stats."""
    info = {
        "cpu_count": psutil.cpu_count(),
        "memory_total_gb": psutil.virtual_memory().total / 1024**3,
        "memory_available_gb": psutil.virtual_memory().available / 1024**3,
    }
    
    if torch.cuda.is_available():
        gpu_info = []
        for gpu in GPUtil.getGPUs():
            gpu_info.append({
                "id": gpu.id,
                "name": gpu.name,
                "memory_total_gb": gpu.memoryTotal / 1024,
                "memory_used_gb": gpu.memoryUsed / 1024,
                "memory_free_gb": gpu.memoryFree / 1024,
                "temperature": gpu.temperature,
                "load": gpu.load
            })
        info["gpus"] = gpu_info
        info["cuda_version"] = torch.version.cuda
        info["pytorch_version"] = torch.__version__
    
    return info


def save_checkpoint(checkpoint_dir: str, **kwargs):
    """Save training checkpoint with metadata."""
    checkpoint_data = {
        "timestamp": str(torch.utils.data.get_worker_info()),
        "system_info": get_system_info(),
        **kwargs
    }
    
    checkpoint_path = os.path.join(checkpoint_dir, "training_state.json")
    with open(checkpoint_path, 'w') as f:
        json.dump(checkpoint_data, f, indent=2, default=str)


def load_checkpoint(checkpoint_dir: str) -> Dict[str, Any]:
    """Load training checkpoint."""
    checkpoint_path = os.path.join(checkpoint_dir, "training_state.json")
    
    if not os.path.exists(checkpoint_path):
        raise FileNotFoundError(f"Checkpoint not found: {checkpoint_path}")
    
    with open(checkpoint_path, 'r') as f:
        return json.load(f)


def setup_logging(log_level: str = "INFO", log_file: Optional[str] = None):
    """Setup logging configuration."""
    log_format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    handlers = [logging.StreamHandler()]
    if log_file:
        os.makedirs(os.path.dirname(log_file), exist_ok=True)
        handlers.append(logging.FileHandler(log_file))
    
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format=log_format,
        handlers=handlers
    )


def validate_config(config: Dict[str, Any]) -> bool:
    """Validate configuration parameters."""
    required_keys = [
        "model.name",
        "training.output_dir",
        "dataset.train_data_dir"
    ]
    
    def check_nested_key(d: Dict, key: str) -> bool:
        keys = key.split('.')
        current = d
        for k in keys:
            if k not in current:
                return False
            current = current[k]
        return True
    
    for key in required_keys:
        if not check_nested_key(config, key):
            logger.error(f"Missing required configuration key: {key}")
            return False
    
    # Validate paths
    train_data_dir = config.get('dataset', {}).get('train_data_dir')
    if train_data_dir and not os.path.exists(train_data_dir):
        logger.error(f"Training data directory does not exist: {train_data_dir}")
        return False
    
    # Validate GPU availability for CUDA operations
    if not torch.cuda.is_available():
        logger.warning("CUDA not available. Training will be very slow on CPU.")
    
    return True


def estimate_memory_requirements(config: Dict[str, Any]) -> Dict[str, float]:
    """Estimate memory requirements for training."""
    # Base model memory (approximate for FLUX)
    base_model_memory = 12.0  # GB for FLUX.1-dev in bfloat16
    
    # LoRA parameters
    qlora_config = config.get('qlora', {})
    lora_rank = qlora_config.get('r', 64)
    lora_memory = (lora_rank * 4 * 1024 * 1024 * 4) / 1024**3  # Rough estimate
    
    # Batch size impact
    batch_size = config.get('training', {}).get('per_device_train_batch_size', 1)
    gradient_accumulation = config.get('training', {}).get('gradient_accumulation_steps', 8)
    effective_batch_size = batch_size * gradient_accumulation
    
    # Image resolution impact
    resolution = config.get('image', {}).get('resolution', 1024)
    image_memory = (resolution * resolution * 3 * 4 * effective_batch_size) / 1024**3
    
    # Optimizer states (AdamW8bit)
    optimizer_memory = lora_memory * 2  # Rough estimate for 8-bit optimizer
    
    total_estimated = base_model_memory + lora_memory + image_memory + optimizer_memory
    
    return {
        "base_model_gb": base_model_memory,
        "lora_parameters_gb": lora_memory,
        "batch_data_gb": image_memory,
        "optimizer_states_gb": optimizer_memory,
        "total_estimated_gb": total_estimated,
        "recommended_gpu_memory_gb": total_estimated * 1.3  # 30% buffer
    }


def create_directory_structure(base_dir: str):
    """Create necessary directory structure for training."""
    directories = [
        "data/train",
        "data/validation", 
        "outputs",
        "logs",
        "models",
        "checkpoints"
    ]
    
    for directory in directories:
        full_path = os.path.join(base_dir, directory)
        os.makedirs(full_path, exist_ok=True)
        logger.info(f"Created directory: {full_path}")


def format_time(seconds: float) -> str:
    """Format time in seconds to human readable format."""
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    seconds = int(seconds % 60)
    
    if hours > 0:
        return f"{hours}h {minutes}m {seconds}s"
    elif minutes > 0:
        return f"{minutes}m {seconds}s"
    else:
        return f"{seconds}s"


def count_parameters(model: torch.nn.Module) -> Dict[str, int]:
    """Count model parameters."""
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    return {
        "total": total_params,
        "trainable": trainable_params,
        "frozen": total_params - trainable_params
    }


def cleanup_memory():
    """Clean up GPU and system memory."""
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        torch.cuda.synchronize()
    
    import gc
    gc.collect()


def check_disk_space(path: str, required_gb: float = 10.0) -> bool:
    """Check if there's enough disk space."""
    try:
        free_space = psutil.disk_usage(path).free / 1024**3
        if free_space < required_gb:
            logger.warning(f"Low disk space: {free_space:.1f}GB available, {required_gb:.1f}GB required")
            return False
        return True
    except Exception as e:
        logger.error(f"Error checking disk space: {e}")
        return False


def get_optimal_batch_size(available_memory_gb: float, image_resolution: int = 1024) -> int:
    """Estimate optimal batch size based on available memory."""
    # Rough estimation based on image size and model requirements
    memory_per_image_gb = (image_resolution * image_resolution * 3 * 4) / 1024**3
    model_overhead_gb = 15.0  # Base model + overhead
    
    available_for_batch = available_memory_gb - model_overhead_gb
    if available_for_batch <= 0:
        return 1
    
    optimal_batch_size = max(1, int(available_for_batch / (memory_per_image_gb * 4)))  # 4x safety factor
    return min(optimal_batch_size, 8)  # Cap at 8 for stability
