"""
Advanced dataset processor with auto-captioning capabilities for FLUX fine-tuning.
Supports multiple captioning models and variable image sizes.
"""

import os
import json
from pathlib import Path
from typing import List, Dict, Optional, Tuple
import torch
from PIL import Image
import pandas as pd
from tqdm import tqdm
from transformers import <PERSON>lipProcessor, BlipForConditionalGeneration
from clip_interrogator import ClipInterrogator, Config
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class AdvancedCaptioner:
    """Advanced image captioning using multiple models."""
    
    def __init__(self, config: Dict):
        self.config = config
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.models = {}
        self._load_models()
    
    def _load_models(self):
        """Load captioning models based on configuration."""
        captioning_config = self.config.get('captioning', {})
        models_to_load = captioning_config.get('models', ['blip2'])
        
        if 'blip2' in models_to_load:
            logger.info("Loading BLIP2 model...")
            model_name = captioning_config.get('blip2_model', 'Salesforce/blip2-opt-2.7b')
            self.models['blip2'] = {
                'processor': BlipProcessor.from_pretrained(model_name),
                'model': BlipForConditionalGeneration.from_pretrained(
                    model_name, 
                    torch_dtype=torch.float16,
                    device_map="auto"
                )
            }
        
        if 'clip_interrogator' in models_to_load:
            logger.info("Loading CLIP Interrogator...")
            ci_config = Config()
            ci_config.clip_model_name = captioning_config.get('clip_model', 'ViT-L-14/openai')
            ci_config.device = self.device
            self.models['clip_interrogator'] = ClipInterrogator(ci_config)
    
    def caption_image(self, image_path: str) -> Dict[str, str]:
        """Generate captions for an image using all loaded models."""
        image = Image.open(image_path).convert('RGB')
        captions = {}
        
        # BLIP2 captioning
        if 'blip2' in self.models:
            try:
                processor = self.models['blip2']['processor']
                model = self.models['blip2']['model']
                
                inputs = processor(image, return_tensors="pt").to(self.device)
                generated_ids = model.generate(**inputs, max_length=50)
                caption = processor.decode(generated_ids[0], skip_special_tokens=True)
                captions['blip2'] = caption
            except Exception as e:
                logger.warning(f"BLIP2 captioning failed: {e}")
                captions['blip2'] = ""
        
        # CLIP Interrogator
        if 'clip_interrogator' in self.models:
            try:
                caption = self.models['clip_interrogator'].interrogate(image)
                captions['clip_interrogator'] = caption
            except Exception as e:
                logger.warning(f"CLIP Interrogator failed: {e}")
                captions['clip_interrogator'] = ""
        
        return captions
    
    def combine_captions(self, captions: Dict[str, str]) -> str:
        """Combine multiple captions into a single enhanced caption."""
        if not captions:
            return ""
        
        # Filter out empty captions
        valid_captions = [cap for cap in captions.values() if cap.strip()]
        
        if not valid_captions:
            return ""
        
        if len(valid_captions) == 1:
            return valid_captions[0]
        
        # Combine captions intelligently
        combined = valid_captions[0]
        for caption in valid_captions[1:]:
            # Add unique elements from other captions
            words = set(caption.lower().split())
            existing_words = set(combined.lower().split())
            new_words = words - existing_words
            
            if new_words:
                combined += f", {', '.join(sorted(new_words))}"
        
        max_length = self.config.get('captioning', {}).get('max_caption_length', 200)
        if len(combined) > max_length:
            combined = combined[:max_length].rsplit(',', 1)[0]
        
        return combined


class DatasetProcessor:
    """Process datasets for FLUX fine-tuning with auto-captioning."""
    
    def __init__(self, config: Dict):
        self.config = config
        self.captioner = AdvancedCaptioner(config) if config.get('captioning', {}).get('enabled', False) else None
        
    def process_directory(self, data_dir: str, output_metadata: Optional[str] = None) -> List[Dict]:
        """Process a directory of images and generate/update captions."""
        data_path = Path(data_dir)
        if not data_path.exists():
            raise ValueError(f"Data directory {data_dir} does not exist")
        
        image_extensions = self.config.get('dataset', {}).get('image_extensions', ['.jpg', '.jpeg', '.png', '.webp'])
        caption_extension = self.config.get('dataset', {}).get('caption_extension', '.txt')
        
        # Find all images
        image_files = []
        for ext in image_extensions:
            image_files.extend(data_path.glob(f"**/*{ext}"))
            image_files.extend(data_path.glob(f"**/*{ext.upper()}"))
        
        logger.info(f"Found {len(image_files)} images in {data_dir}")
        
        dataset_items = []
        
        for image_path in tqdm(image_files, desc="Processing images"):
            try:
                # Check image validity
                with Image.open(image_path) as img:
                    width, height = img.size
                    if width < 64 or height < 64:  # Skip very small images
                        continue
                
                # Look for existing caption file
                caption_path = image_path.with_suffix(caption_extension)
                existing_caption = ""
                
                if caption_path.exists():
                    with open(caption_path, 'r', encoding='utf-8') as f:
                        existing_caption = f.read().strip()
                
                # Generate new caption if needed
                final_caption = existing_caption
                if self.captioner and (not existing_caption or len(existing_caption) < 10):
                    logger.info(f"Generating caption for {image_path.name}")
                    captions = self.captioner.caption_image(str(image_path))
                    
                    if self.config.get('captioning', {}).get('combine_captions', True):
                        generated_caption = self.captioner.combine_captions(captions)
                    else:
                        # Use the first available caption
                        generated_caption = next(iter(captions.values()), "")
                    
                    if generated_caption:
                        final_caption = generated_caption
                        # Save the generated caption
                        with open(caption_path, 'w', encoding='utf-8') as f:
                            f.write(final_caption)
                
                dataset_items.append({
                    'image_path': str(image_path),
                    'caption': final_caption,
                    'width': width,
                    'height': height,
                    'aspect_ratio': width / height
                })
                
            except Exception as e:
                logger.warning(f"Failed to process {image_path}: {e}")
                continue
        
        logger.info(f"Successfully processed {len(dataset_items)} images")
        
        # Save metadata if requested
        if output_metadata:
            df = pd.DataFrame(dataset_items)
            df.to_csv(output_metadata, index=False)
            logger.info(f"Saved metadata to {output_metadata}")
        
        return dataset_items
    
    def validate_dataset(self, dataset_items: List[Dict]) -> Dict:
        """Validate the processed dataset and return statistics."""
        if not dataset_items:
            return {"error": "No valid dataset items found"}
        
        df = pd.DataFrame(dataset_items)
        
        stats = {
            "total_images": len(df),
            "images_with_captions": len(df[df['caption'].str.len() > 0]),
            "average_caption_length": df['caption'].str.len().mean(),
            "resolution_stats": {
                "min_width": df['width'].min(),
                "max_width": df['width'].max(),
                "min_height": df['height'].min(),
                "max_height": df['height'].max(),
                "avg_aspect_ratio": df['aspect_ratio'].mean()
            }
        }
        
        return stats
