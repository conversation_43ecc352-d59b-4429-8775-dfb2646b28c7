#!/usr/bin/env python3
"""
RunPod-specific setup script for FLUX QLoRA fine-tuning.
Optimized for RunPod A40 cloud GPU environment.
"""

import os
import subprocess
import sys
import json
import time
from pathlib import Path

def run_command(command, description, check=True):
    """Run a command and handle errors."""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=check, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ {description} completed successfully")
            return True, result.stdout
        else:
            print(f"⚠️ {description} completed with warnings")
            print(f"Output: {result.stdout}")
            print(f"Error: {result.stderr}")
            return False, result.stderr
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed:")
        print(f"Error: {e.stderr}")
        return False, e.stderr

def detect_runpod_environment():
    """Detect if we're running on RunPod."""
    runpod_indicators = [
        os.path.exists('/workspace'),
        os.environ.get('RUNPOD_POD_ID'),
        os.environ.get('RUNPOD_POD_HOSTNAME'),
        'runpod' in os.environ.get('HOSTNAME', '').lower()
    ]
    
    is_runpod = any(runpod_indicators)
    
    if is_runpod:
        print("✅ RunPod environment detected")
        print(f"   Pod ID: {os.environ.get('RUNPOD_POD_ID', 'Unknown')}")
        print(f"   Hostname: {os.environ.get('HOSTNAME', 'Unknown')}")
    else:
        print("⚠️ RunPod environment not detected - proceeding with local setup")
    
    return is_runpod

def setup_runpod_paths():
    """Setup RunPod-specific directory structure."""
    print("📁 Setting up RunPod directory structure...")
    
    # RunPod workspace directories
    directories = [
        "/workspace/data/train",
        "/workspace/data/validation",
        "/workspace/outputs",
        "/workspace/logs", 
        "/workspace/models",
        "/workspace/checkpoints",
        "/workspace/generated_images",
        "/workspace/network_storage"  # For persistent storage
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"📁 Created: {directory}")
    
    # Create symlinks for convenience
    workspace_links = [
        ("data", "/workspace/data"),
        ("outputs", "/workspace/outputs"),
        ("logs", "/workspace/logs"),
        ("models", "/workspace/models")
    ]
    
    for link_name, target in workspace_links:
        if not os.path.exists(link_name):
            try:
                os.symlink(target, link_name)
                print(f"🔗 Created symlink: {link_name} -> {target}")
            except OSError:
                print(f"⚠️ Could not create symlink: {link_name}")

def install_runpod_optimized_packages():
    """Install packages optimized for RunPod environment."""
    print("📦 Installing RunPod-optimized packages...")
    
    # Upgrade pip with RunPod optimizations
    run_command(f"{sys.executable} -m pip install --upgrade pip", "Upgrading pip")
    
    # Install PyTorch with CUDA 12.1 (RunPod standard)
    pytorch_command = f"{sys.executable} -m pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121"
    run_command(pytorch_command, "Installing PyTorch with CUDA 12.1")
    
    # Install core requirements
    run_command(f"{sys.executable} -m pip install -r requirements.txt", "Installing core requirements")
    
    # Install RunPod-specific optimizations
    runpod_packages = [
        "flash-attn --no-build-isolation",  # Faster attention for A40
        "xformers",  # Memory efficient transformers
        "triton",    # GPU kernel optimizations
        "ninja",     # Faster compilation
    ]
    
    for package in runpod_packages:
        success, _ = run_command(f"{sys.executable} -m pip install {package}", 
                               f"Installing {package.split()[0]}", check=False)
        if not success:
            print(f"⚠️ Optional package {package.split()[0]} failed to install")

def setup_huggingface_auth():
    """Setup HuggingFace authentication for FLUX model access."""
    print("🔑 Setting up HuggingFace authentication...")
    
    hf_token = os.environ.get('HF_TOKEN')
    if hf_token:
        print("✅ HF_TOKEN found in environment")
        # Save token to HuggingFace cache
        run_command(f"huggingface-cli login --token {hf_token}", "Logging in to HuggingFace")
    else:
        print("⚠️ HF_TOKEN not found in environment variables")
        print("   Please set HF_TOKEN in RunPod environment or run: huggingface-cli login")
        print("   You need access to black-forest-labs/FLUX.1-dev model")

def setup_wandb_auth():
    """Setup Weights & Biases authentication."""
    print("📊 Setting up Weights & Biases...")
    
    wandb_key = os.environ.get('WANDB_API_KEY')
    if wandb_key:
        print("✅ WANDB_API_KEY found in environment")
        run_command(f"wandb login {wandb_key}", "Logging in to W&B")
    else:
        print("⚠️ WANDB_API_KEY not found in environment variables")
        print("   Please set WANDB_API_KEY in RunPod environment or run: wandb login")

def optimize_runpod_performance():
    """Apply RunPod-specific performance optimizations."""
    print("⚡ Applying RunPod performance optimizations...")
    
    # Set environment variables for optimal performance
    env_vars = {
        'CUDA_LAUNCH_BLOCKING': '0',
        'TORCH_CUDNN_V8_API_ENABLED': '1',
        'PYTORCH_CUDA_ALLOC_CONF': 'max_split_size_mb:512',
        'TOKENIZERS_PARALLELISM': 'false',  # Avoid tokenizer warnings
        'OMP_NUM_THREADS': '8',  # Optimize CPU usage
        'MKL_NUM_THREADS': '8',
    }
    
    for var, value in env_vars.items():
        os.environ[var] = value
        print(f"   Set {var}={value}")
    
    # Create performance optimization script
    perf_script = """#!/bin/bash
# RunPod Performance Optimization Script
export CUDA_LAUNCH_BLOCKING=0
export TORCH_CUDNN_V8_API_ENABLED=1
export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512
export TOKENIZERS_PARALLELISM=false
export OMP_NUM_THREADS=8
export MKL_NUM_THREADS=8

# GPU performance mode
nvidia-smi -pm 1
nvidia-smi -ac 1215,1410  # Set memory and graphics clocks for A40

echo "RunPod performance optimizations applied!"
"""
    
    with open('/workspace/optimize_performance.sh', 'w') as f:
        f.write(perf_script)
    
    os.chmod('/workspace/optimize_performance.sh', 0o755)
    print("📝 Created performance optimization script: /workspace/optimize_performance.sh")

def create_runpod_startup_script():
    """Create a startup script for RunPod."""
    startup_script = """#!/bin/bash
# RunPod FLUX QLoRA Startup Script

echo "🚀 Starting FLUX QLoRA Fine-tuning on RunPod A40"
echo "================================================"

# Change to workspace directory
cd /workspace

# Apply performance optimizations
if [ -f "optimize_performance.sh" ]; then
    ./optimize_performance.sh
fi

# Validate setup
echo "🔍 Validating setup..."
python scripts/validate_setup.py

# Check GPU status
echo "🖥️ GPU Status:"
nvidia-smi

echo "✅ RunPod startup completed!"
echo ""
echo "Next steps:"
echo "1. Upload your training images to /workspace/data/train/"
echo "2. Run: python scripts/prepare_dataset.py --data_dir /workspace/data/train"
echo "3. Run: python scripts/train.py"
echo ""
echo "Or use the Makefile commands:"
echo "  make prepare"
echo "  make train"
"""
    
    with open('/workspace/startup.sh', 'w') as f:
        f.write(startup_script)
    
    os.chmod('/workspace/startup.sh', 0o755)
    print("📝 Created startup script: /workspace/startup.sh")

def check_runpod_resources():
    """Check RunPod resource availability."""
    print("🔍 Checking RunPod resources...")
    
    # Check GPU
    success, gpu_info = run_command("nvidia-smi --query-gpu=name,memory.total,memory.free --format=csv,noheader,nounits", 
                                   "Checking GPU information", check=False)
    
    if success:
        lines = gpu_info.strip().split('\n')
        for i, line in enumerate(lines):
            parts = line.split(', ')
            if len(parts) >= 3:
                name, total_mem, free_mem = parts[0], parts[1], parts[2]
                print(f"   GPU {i}: {name}")
                print(f"   Total Memory: {total_mem} MB")
                print(f"   Free Memory: {free_mem} MB")
                
                # Check if it's A40
                if 'A40' in name:
                    print("   ✅ A40 GPU detected - optimal for FLUX training")
                elif int(total_mem) >= 20000:
                    print("   ✅ High-memory GPU detected - suitable for FLUX training")
                else:
                    print("   ⚠️ GPU may have insufficient memory for FLUX training")
    
    # Check disk space
    success, disk_info = run_command("df -h /workspace", "Checking disk space", check=False)
    if success:
        print("   Disk Space:")
        print(f"   {disk_info}")
    
    # Check CPU
    success, cpu_info = run_command("nproc", "Checking CPU cores", check=False)
    if success:
        print(f"   CPU Cores: {cpu_info.strip()}")

def create_runpod_config():
    """Create RunPod-specific configuration."""
    runpod_config = {
        "runpod_optimized": True,
        "workspace_path": "/workspace",
        "persistent_storage": True,
        "auto_checkpoint_sync": True,
        "gpu_type": "A40",
        "recommended_settings": {
            "batch_size": 2,
            "gradient_accumulation": 4,
            "num_workers": 8,
            "mixed_precision": "bf16"
        }
    }
    
    with open('/workspace/runpod_config.json', 'w') as f:
        json.dump(runpod_config, f, indent=2)
    
    print("📝 Created RunPod configuration: /workspace/runpod_config.json")

def main():
    """Main RunPod setup function."""
    print("🚀 FLUX QLoRA RunPod A40 Setup")
    print("=" * 50)
    
    # Detect environment
    is_runpod = detect_runpod_environment()
    
    # Setup paths
    setup_runpod_paths()
    
    # Install packages
    install_runpod_optimized_packages()
    
    # Setup authentication
    setup_huggingface_auth()
    setup_wandb_auth()
    
    # Performance optimizations
    optimize_runpod_performance()
    
    # Create scripts
    create_runpod_startup_script()
    create_runpod_config()
    
    # Check resources
    check_runpod_resources()
    
    print("\n🎉 RunPod setup completed successfully!")
    print("\nRunPod-specific features enabled:")
    print("✅ Optimized for A40 GPU (48GB VRAM)")
    print("✅ Batch size 2 with gradient accumulation 4")
    print("✅ Persistent storage integration")
    print("✅ Automatic checkpoint syncing")
    print("✅ Performance optimizations applied")
    print("✅ HuggingFace and W&B authentication")
    
    print("\nNext steps:")
    print("1. Upload training images to /workspace/data/train/")
    print("2. Run: ./startup.sh (or python scripts/validate_setup.py)")
    print("3. Run: make prepare")
    print("4. Run: make train")
    
    print("\nFor monitoring:")
    print("- W&B dashboard will show training progress")
    print("- Checkpoints saved to /workspace/outputs/")
    print("- Logs available in /workspace/logs/")
    
    return 0

if __name__ == "__main__":
    exit(main())
