#!/usr/bin/env python3
"""
Test script to demonstrate auto-captioning functionality.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

import argparse
from PIL import Image
import requests
from io import BytesIO
from src.dataset_processor import AdvancedCaptioner
from src.utils import load_config
from rich.console import Console
from rich.table import Table

console = Console()


def download_sample_image(url: str, save_path: str):
    """Download a sample image for testing."""
    try:
        response = requests.get(url)
        response.raise_for_status()
        
        image = Image.open(BytesIO(response.content))
        image.save(save_path)
        console.print(f"[green]Downloaded sample image to {save_path}[/green]")
        return True
    except Exception as e:
        console.print(f"[red]Failed to download image: {e}[/red]")
        return False


def main():
    parser = argparse.ArgumentParser(description="Test auto-captioning functionality")
    parser.add_argument("--image", type=str, default=None,
                       help="Path to image file (will download sample if not provided)")
    parser.add_argument("--config", type=str, default="config/training_config.yaml",
                       help="Configuration file")
    parser.add_argument("--download_sample", action="store_true",
                       help="Download a sample image for testing")
    
    args = parser.parse_args()
    
    console.print("[bold blue]FLUX Auto-Captioning Test[/bold blue]")
    console.print("=" * 40)
    
    # Load configuration
    try:
        config = load_config(args.config)
        # Enable captioning for testing
        config.setdefault('captioning', {})['enabled'] = True
        config['captioning']['models'] = ['blip2', 'clip_interrogator']
    except Exception as e:
        console.print(f"[red]Error loading config: {e}[/red]")
        return 1
    
    # Handle image input
    image_path = args.image
    
    if not image_path or args.download_sample:
        # Download a sample image
        sample_url = "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800"
        image_path = "sample_image.jpg"
        
        console.print("[cyan]Downloading sample image...[/cyan]")
        if not download_sample_image(sample_url, image_path):
            return 1
    
    if not os.path.exists(image_path):
        console.print(f"[red]Image file not found: {image_path}[/red]")
        return 1
    
    # Display image info
    try:
        with Image.open(image_path) as img:
            width, height = img.size
            mode = img.mode
            
        console.print(f"[green]Image loaded: {image_path}[/green]")
        console.print(f"[dim]Size: {width}x{height}, Mode: {mode}[/dim]")
    except Exception as e:
        console.print(f"[red]Error loading image: {e}[/red]")
        return 1
    
    # Initialize captioner
    console.print("\n[cyan]Initializing captioning models...[/cyan]")
    try:
        captioner = AdvancedCaptioner(config)
        console.print("[green]Captioning models loaded successfully![/green]")
    except Exception as e:
        console.print(f"[red]Error initializing captioner: {e}[/red]")
        console.print("[yellow]Make sure you have installed the required packages:[/yellow]")
        console.print("pip install salesforce-blip clip-interrogator")
        return 1
    
    # Generate captions
    console.print("\n[cyan]Generating captions...[/cyan]")
    try:
        captions = captioner.caption_image(image_path)
        
        # Display results
        results_table = Table(title="Generated Captions")
        results_table.add_column("Model", style="cyan")
        results_table.add_column("Caption", style="white")
        results_table.add_column("Length", style="dim")
        
        for model, caption in captions.items():
            results_table.add_row(model, caption, str(len(caption)))
        
        console.print(results_table)
        
        # Generate combined caption
        if len(captions) > 1:
            combined = captioner.combine_captions(captions)
            console.print(f"\n[bold green]Combined Caption:[/bold green]")
            console.print(f"[white]{combined}[/white]")
            console.print(f"[dim]Length: {len(combined)} characters[/dim]")
        
        # Save captions to file
        caption_file = image_path.replace('.jpg', '.txt').replace('.png', '.txt')
        with open(caption_file, 'w', encoding='utf-8') as f:
            if len(captions) > 1:
                f.write(combined)
            else:
                f.write(next(iter(captions.values()), ""))
        
        console.print(f"\n[green]Caption saved to: {caption_file}[/green]")
        
    except Exception as e:
        console.print(f"[red]Error generating captions: {e}[/red]")
        return 1
    
    console.print("\n[bold green]✅ Captioning test completed successfully![/bold green]")
    
    # Cleanup sample image if downloaded
    if args.download_sample and os.path.exists("sample_image.jpg"):
        console.print("[dim]Cleaning up sample image...[/dim]")
        os.remove("sample_image.jpg")
        if os.path.exists("sample_image.txt"):
            os.remove("sample_image.txt")
    
    return 0


if __name__ == "__main__":
    exit(main())
