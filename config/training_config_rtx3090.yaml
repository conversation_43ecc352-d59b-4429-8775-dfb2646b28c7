# FLUX QLoRA Training Configuration - Optimized for RTX 3090 (24GB VRAM)
model:
  name: "black-forest-labs/FLUX.1-dev"
  cache_dir: "./models"
  torch_dtype: "bfloat16"
  
# QLoRA Configuration - Reduced for memory efficiency
qlora:
  r: 32  # Reduced LoRA rank
  lora_alpha: 64  # Reduced LoRA alpha
  lora_dropout: 0.1
  target_modules:
    - "to_q"
    - "to_k" 
    - "to_v"
    - "to_out.0"
  bias: "none"
  task_type: "FEATURE_EXTRACTION"
  
# Quantization
quantization:
  load_in_4bit: true
  bnb_4bit_compute_dtype: "bfloat16"
  bnb_4bit_use_double_quant: true
  bnb_4bit_quant_type: "nf4"

# Training Parameters - Memory optimized
training:
  output_dir: "./outputs"
  num_train_epochs: 15  # More epochs to compensate for smaller model
  per_device_train_batch_size: 1
  gradient_accumulation_steps: 16  # Increased to maintain effective batch size
  learning_rate: 2e-4  # Slightly higher LR
  weight_decay: 0.01
  warmup_steps: 200
  max_grad_norm: 1.0
  save_steps: 500
  logging_steps: 10
  eval_steps: 500
  save_total_limit: 2  # Reduced to save disk space
  dataloader_num_workers: 2  # Reduced for memory
  remove_unused_columns: false
  
# Image Processing - Reduced resolution
image:
  resolution: 768  # Reduced from 1024
  min_resolution: 512
  max_resolution: 1024  # Reduced max
  center_crop: false
  random_flip: true
  
# Dataset
dataset:
  train_data_dir: "./data/train"
  validation_data_dir: "./data/validation"
  caption_extension: ".txt"
  image_extensions: [".jpg", ".jpeg", ".png", ".webp"]
  max_sequence_length: 256  # Reduced
  
# Auto-captioning
captioning:
  enabled: true
  models:
    - "blip2"  # Only BLIP2 to save memory
  blip2_model: "Salesforce/blip2-opt-2.7b"
  max_caption_length: 150
  combine_captions: false
  
# Monitoring
monitoring:
  use_wandb: true
  project_name: "flux-qlora-rtx3090"
  run_name: null
  
# Hardware Optimization - Aggressive memory saving
hardware:
  mixed_precision: "bf16"
  gradient_checkpointing: true
  use_cpu_offload: true  # Enable CPU offloading
  pin_memory: false  # Disable to save memory
