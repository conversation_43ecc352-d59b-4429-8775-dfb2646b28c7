# FLUX QLoRA Training Configuration
model:
  name: "black-forest-labs/FLUX.1-dev"
  cache_dir: "./models"
  torch_dtype: "bfloat16"
  
# QLoRA Configuration
qlora:
  r: 64  # LoRA rank
  lora_alpha: 128  # LoRA alpha
  lora_dropout: 0.1
  target_modules:
    - "to_q"
    - "to_k" 
    - "to_v"
    - "to_out.0"
    - "proj_in"
    - "proj_out"
  bias: "none"
  task_type: "FEATURE_EXTRACTION"
  
# Quantization
quantization:
  load_in_4bit: true
  bnb_4bit_compute_dtype: "bfloat16"
  bnb_4bit_use_double_quant: true
  bnb_4bit_quant_type: "nf4"

# Training Parameters
training:
  output_dir: "./outputs"
  num_train_epochs: 10
  per_device_train_batch_size: 1
  gradient_accumulation_steps: 8
  learning_rate: 1e-4
  weight_decay: 0.01
  warmup_steps: 100
  max_grad_norm: 1.0
  save_steps: 500
  logging_steps: 10
  eval_steps: 500
  save_total_limit: 3
  dataloader_num_workers: 4
  remove_unused_columns: false
  
# Image Processing
image:
  resolution: 1024
  min_resolution: 512
  max_resolution: 1536
  center_crop: false
  random_flip: true
  
# Dataset
dataset:
  train_data_dir: "./data/train"
  validation_data_dir: "./data/validation"
  caption_extension: ".txt"
  image_extensions: [".jpg", ".jpeg", ".png", ".webp"]
  max_sequence_length: 512
  
# Auto-captioning
captioning:
  enabled: true
  models:
    - "blip2"
    - "clip_interrogator"
  blip2_model: "Salesforce/blip2-opt-2.7b"
  clip_model: "ViT-L-14/openai"
  max_caption_length: 200
  combine_captions: true
  
# Monitoring
monitoring:
  use_wandb: true
  project_name: "flux-qlora-finetuning"
  run_name: null  # Will be auto-generated
  
# Hardware Optimization
hardware:
  mixed_precision: "bf16"
  gradient_checkpointing: true
  use_cpu_offload: false
  pin_memory: true
