# FLUX QLoRA Training Configuration - Optimized for RunPod A40
model:
  name: "black-forest-labs/FLUX.1-dev"
  cache_dir: "/workspace/models"  # RunPod workspace path
  torch_dtype: "bfloat16"
  use_auth_token: true  # For HuggingFace authentication

# QLoRA Configuration
qlora:
  r: 64  # LoRA rank
  lora_alpha: 128  # LoRA alpha
  lora_dropout: 0.1
  target_modules:
    - "to_q"
    - "to_k"
    - "to_v"
    - "to_out.0"
    - "proj_in"
    - "proj_out"
  bias: "none"
  task_type: "FEATURE_EXTRACTION"

# Quantization
quantization:
  load_in_4bit: true
  bnb_4bit_compute_dtype: "bfloat16"
  bnb_4bit_use_double_quant: true
  bnb_4bit_quant_type: "nf4"

# Training Parameters - RunPod A40 Optimized
training:
  output_dir: "/workspace/outputs"
  num_train_epochs: 10
  per_device_train_batch_size: 2  # A40 can handle batch size 2
  gradient_accumulation_steps: 4  # Reduced due to larger batch size
  learning_rate: 1e-4
  weight_decay: 0.01
  warmup_steps: 100
  max_grad_norm: 1.0
  save_steps: 250  # More frequent saves for cloud stability
  logging_steps: 5   # More frequent logging
  eval_steps: 250
  save_total_limit: 5  # Keep more checkpoints for cloud reliability
  dataloader_num_workers: 8  # A40 has good CPU
  remove_unused_columns: false
  resume_from_checkpoint: true  # Auto-resume capability

# Image Processing
image:
  resolution: 1024
  min_resolution: 512
  max_resolution: 1536
  center_crop: false
  random_flip: true

# Dataset - RunPod paths
dataset:
  train_data_dir: "/workspace/data/train"
  validation_data_dir: "/workspace/data/validation"
  caption_extension: ".txt"
  image_extensions: [".jpg", ".jpeg", ".png", ".webp"]
  max_sequence_length: 512

# Auto-captioning
captioning:
  enabled: true
  models:
    - "blip2"
    - "clip_interrogator"
  blip2_model: "Salesforce/blip2-opt-2.7b"
  clip_model: "ViT-L-14/openai"
  max_caption_length: 200
  combine_captions: true

# Monitoring - RunPod optimized
monitoring:
  use_wandb: true
  project_name: "flux-qlora-runpod-a40"
  run_name: null  # Will be auto-generated
  log_model: true  # Save model artifacts to W&B
  save_code: true  # Save code to W&B

# Hardware Optimization - RunPod A40 specific
hardware:
  mixed_precision: "bf16"
  gradient_checkpointing: true
  use_cpu_offload: false  # A40 has enough VRAM
  pin_memory: true
  compile_model: false  # Disable torch.compile for stability

# RunPod specific settings
runpod:
  auto_shutdown: false  # Don't auto-shutdown on completion
  save_to_network_storage: true  # Save to persistent storage
  checkpoint_sync_interval: 1000  # Sync checkpoints every N steps
  use_fast_tokenizer: true
  preload_models: true  # Preload models for faster startup
