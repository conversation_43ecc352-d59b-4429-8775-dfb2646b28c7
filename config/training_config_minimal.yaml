# FLUX QLoRA Training Configuration - Minimal Memory Usage (16GB+ VRAM)
model:
  name: "black-forest-labs/FLUX.1-dev"
  cache_dir: "./models"
  torch_dtype: "bfloat16"
  
# QLoRA Configuration - Minimal settings
qlora:
  r: 16  # Very low LoRA rank
  lora_alpha: 32  # Low LoRA alpha
  lora_dropout: 0.05
  target_modules:
    - "to_q"
    - "to_v"  # Only Q and V projections
  bias: "none"
  task_type: "FEATURE_EXTRACTION"
  
# Quantization
quantization:
  load_in_4bit: true
  bnb_4bit_compute_dtype: "bfloat16"
  bnb_4bit_use_double_quant: true
  bnb_4bit_quant_type: "nf4"

# Training Parameters - Extreme memory optimization
training:
  output_dir: "./outputs"
  num_train_epochs: 20  # More epochs for smaller model
  per_device_train_batch_size: 1
  gradient_accumulation_steps: 32  # Very high accumulation
  learning_rate: 5e-4  # Higher LR to compensate
  weight_decay: 0.005
  warmup_steps: 500
  max_grad_norm: 0.5
  save_steps: 1000
  logging_steps: 25
  eval_steps: 1000
  save_total_limit: 1  # Only keep latest checkpoint
  dataloader_num_workers: 1
  remove_unused_columns: false
  
# Image Processing - Very conservative
image:
  resolution: 512  # Minimum resolution
  min_resolution: 512
  max_resolution: 768
  center_crop: true  # Force square crops
  random_flip: false  # Disable augmentation
  
# Dataset
dataset:
  train_data_dir: "./data/train"
  validation_data_dir: "./data/validation"
  caption_extension: ".txt"
  image_extensions: [".jpg", ".jpeg", ".png", ".webp"]
  max_sequence_length: 128  # Very short sequences
  
# Auto-captioning - Disabled to save memory during preprocessing
captioning:
  enabled: false
  
# Monitoring
monitoring:
  use_wandb: false  # Disable to save memory
  project_name: "flux-qlora-minimal"
  run_name: null
  
# Hardware Optimization - Maximum memory saving
hardware:
  mixed_precision: "bf16"
  gradient_checkpointing: true
  use_cpu_offload: true
  pin_memory: false
