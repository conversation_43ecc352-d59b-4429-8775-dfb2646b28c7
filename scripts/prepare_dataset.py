#!/usr/bin/env python3
"""
Dataset preparation script with advanced auto-captioning for FLUX fine-tuning.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

import argparse
import logging
from pathlib import Path
from src.dataset_processor import DatasetProcessor
from src.utils import load_config, setup_logging, get_system_info
from rich.console import Console
from rich.table import Table
from rich.progress import Progress

console = Console()


def main():
    parser = argparse.ArgumentParser(description="Prepare dataset for FLUX fine-tuning with auto train/val split")
    parser.add_argument("--config", type=str, default="config/training_config.yaml",
                       help="Path to configuration file")
    parser.add_argument("--data_dir", type=str, default=None,
                       help="Directory containing images to process (overrides config)")
    parser.add_argument("--output_dir", type=str, default=None,
                       help="Output directory for train/val split (overrides config)")
    parser.add_argument("--val_split", type=float, default=None,
                       help="Validation split ratio (0.0-1.0, overrides config)")
    parser.add_argument("--no_split", action="store_true",
                       help="Disable automatic train/val splitting")
    parser.add_argument("--copy_files", action="store_true",
                       help="Copy files instead of creating symlinks")
    parser.add_argument("--output_metadata", type=str, default=None,
                       help="Path to save dataset metadata CSV")
    parser.add_argument("--force_recaption", action="store_true",
                       help="Force re-captioning of images that already have captions")
    parser.add_argument("--log_level", type=str, default="INFO",
                       choices=["DEBUG", "INFO", "WARNING", "ERROR"])

    args = parser.parse_args()

    # Setup logging
    setup_logging(args.log_level, "logs/dataset_preparation.log")
    logger = logging.getLogger(__name__)

    console.print("[bold blue]FLUX Dataset Preparation Tool[/bold blue]")
    console.print("=" * 50)

    # Load configuration
    try:
        config = load_config(args.config)
        logger.info(f"Loaded configuration from {args.config}")
    except Exception as e:
        console.print(f"[red]Error loading configuration: {e}[/red]")
        return 1

    # Override captioning settings if force_recaption is enabled
    if args.force_recaption:
        config.setdefault('captioning', {})['force_recaption'] = True

    # Display system information
    system_info = get_system_info()

    info_table = Table(title="System Information")
    info_table.add_column("Component", style="cyan")
    info_table.add_column("Details", style="white")

    info_table.add_row("CPU Cores", str(system_info['cpu_count']))
    info_table.add_row("Total Memory", f"{system_info['memory_total_gb']:.1f} GB")
    info_table.add_row("Available Memory", f"{system_info['memory_available_gb']:.1f} GB")

    if 'gpus' in system_info:
        for i, gpu in enumerate(system_info['gpus']):
            info_table.add_row(f"GPU {i}", f"{gpu['name']} ({gpu['memory_total_gb']:.1f} GB)")

    console.print(info_table)
    console.print()

    # Determine data directory
    data_dir = args.data_dir or config.get('dataset', {}).get('input_data_dir')
    if not data_dir:
        # Fallback to train_data_dir if no input_data_dir specified
        data_dir = config.get('dataset', {}).get('train_data_dir', './data/images')

    # Validate data directory
    data_path = Path(data_dir)
    if not data_path.exists():
        console.print(f"[red]Error: Data directory {data_dir} does not exist[/red]")
        console.print(f"[yellow]Please create the directory and add your images:[/yellow]")
        console.print(f"  mkdir -p {data_dir}")
        console.print(f"  # Copy your images to {data_dir}")
        return 1

    # Initialize dataset processor
    try:
        processor = DatasetProcessor(config)
        logger.info("Dataset processor initialized")
    except Exception as e:
        console.print(f"[red]Error initializing dataset processor: {e}[/red]")
        logger.error(f"Dataset processor initialization failed: {e}")
        return 1

    # Determine processing mode
    auto_split = config.get('dataset', {}).get('auto_split', True) and not args.no_split
    val_split = args.val_split or config.get('dataset', {}).get('validation_split', 0.1)
    copy_files = args.copy_files or config.get('dataset', {}).get('copy_files', False)

    console.print(f"[green]Processing images in: {data_dir}[/green]")

    if auto_split:
        console.print(f"[cyan]Auto-splitting enabled: {val_split*100:.1f}% for validation[/cyan]")
        console.print(f"[cyan]File handling: {'Copy' if copy_files else 'Symlink'}[/cyan]")

    try:
        with Progress() as progress:
            task = progress.add_task("Processing dataset...", total=None)

            if auto_split:
                # Use automatic train/val splitting
                output_dir = args.output_dir or str(Path(data_dir).parent / "processed_data")

                train_dir, val_dir, dataset_items = processor.process_and_split(
                    data_dir,
                    output_base_dir=output_dir,
                    val_split=val_split,
                    copy_files=copy_files
                )

                console.print(f"[green]Train directory: {train_dir}[/green]")
                console.print(f"[green]Validation directory: {val_dir}[/green]")

            else:
                # Process without splitting (original behavior)
                dataset_items = processor.process_directory(
                    data_dir,
                    args.output_metadata
                )

            progress.update(task, completed=100, total=100)

        logger.info(f"Dataset processing completed. Found {len(dataset_items)} valid items")

    except Exception as e:
        console.print(f"[red]Error during dataset processing: {e}[/red]")
        logger.error(f"Dataset processing failed: {e}")
        return 1

    # Validate and display statistics
    try:
        stats = processor.validate_dataset(dataset_items)

        stats_table = Table(title="Dataset Statistics")
        stats_table.add_column("Metric", style="cyan")
        stats_table.add_column("Value", style="white")

        stats_table.add_row("Total Images", str(stats['total_images']))
        stats_table.add_row("Images with Captions", str(stats['images_with_captions']))
        stats_table.add_row("Average Caption Length", f"{stats['average_caption_length']:.1f} chars")

        res_stats = stats['resolution_stats']
        stats_table.add_row("Min Resolution", f"{res_stats['min_width']}x{res_stats['min_height']}")
        stats_table.add_row("Max Resolution", f"{res_stats['max_width']}x{res_stats['max_height']}")
        stats_table.add_row("Avg Aspect Ratio", f"{res_stats['avg_aspect_ratio']:.2f}")

        console.print(stats_table)

        # Check for potential issues
        issues = []
        if stats['images_with_captions'] < stats['total_images']:
            missing_captions = stats['total_images'] - stats['images_with_captions']
            issues.append(f"{missing_captions} images without captions")

        if stats['average_caption_length'] < 20:
            issues.append("Average caption length is quite short")

        if res_stats['min_width'] < 512 or res_stats['min_height'] < 512:
            issues.append("Some images are smaller than recommended minimum (512px)")

        if issues:
            console.print("\n[yellow]Potential Issues:[/yellow]")
            for issue in issues:
                console.print(f"  • {issue}")

    except Exception as e:
        console.print(f"[red]Error validating dataset: {e}[/red]")
        logger.error(f"Dataset validation failed: {e}")
        return 1

    # Save processed dataset info
    if args.output_metadata:
        console.print(f"\n[green]Dataset metadata saved to: {args.output_metadata}[/green]")

    console.print("\n[bold green]Dataset preparation completed successfully![/bold green]")

    if auto_split:
        train_count = len([item for item in dataset_items if 'train' in item.get('split', 'train')])
        val_count = len(dataset_items) - train_count
        console.print(f"Ready for training with {train_count} training images and {val_count} validation images")
        console.print(f"[dim]Training data: {config.get('dataset', {}).get('train_data_dir', train_dir)}[/dim]")
        console.print(f"[dim]Validation data: {config.get('dataset', {}).get('validation_data_dir', val_dir)}[/dim]")
    else:
        console.print(f"Ready for training with {len(dataset_items)} images")

    console.print("\n[yellow]Next steps:[/yellow]")
    console.print("1. Review the generated captions in the .txt files")
    console.print("2. Adjust training configuration if needed")
    console.print("3. Run: python scripts/train.py")

    return 0


if __name__ == "__main__":
    exit(main())
