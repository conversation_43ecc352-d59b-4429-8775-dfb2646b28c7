#!/usr/bin/env python3
"""
Inference script for fine-tuned FLUX model.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

import argparse
import torch
from PIL import Image
from diffusers import FluxPipeline
from peft import PeftModel
import logging
from pathlib import Path
from src.utils import load_config, setup_logging
from rich.console import Console
from rich.progress import Progress
import time

console = Console()


def load_fine_tuned_model(base_model_path: str, lora_path: str, device: str = "cuda"):
    """Load the fine-tuned FLUX model with LoRA weights."""
    console.print("[green]Loading base FLUX model...[/green]")
    
    # Load base pipeline
    pipeline = FluxPipeline.from_pretrained(
        base_model_path,
        torch_dtype=torch.bfloat16,
        device_map="auto"
    )
    
    console.print("[green]Loading LoRA weights...[/green]")
    
    # Load LoRA weights
    pipeline.transformer = PeftModel.from_pretrained(
        pipeline.transformer,
        lora_path,
        torch_dtype=torch.bfloat16
    )
    
    # Merge LoRA weights for faster inference (optional)
    pipeline.transformer = pipeline.transformer.merge_and_unload()
    
    return pipeline


def generate_image(pipeline, prompt: str, config: dict) -> Image.Image:
    """Generate an image from a text prompt."""
    
    # Generation parameters
    height = config.get('height', 1024)
    width = config.get('width', 1024)
    num_inference_steps = config.get('num_inference_steps', 50)
    guidance_scale = config.get('guidance_scale', 7.5)
    seed = config.get('seed', None)
    
    # Set seed for reproducibility
    if seed is not None:
        torch.manual_seed(seed)
    
    console.print(f"[cyan]Generating image for prompt: '{prompt}'[/cyan]")
    console.print(f"[dim]Resolution: {width}x{height}, Steps: {num_inference_steps}, Guidance: {guidance_scale}[/dim]")
    
    start_time = time.time()
    
    with Progress() as progress:
        task = progress.add_task("Generating...", total=num_inference_steps)
        
        def callback(step, timestep, latents):
            progress.update(task, advance=1)
        
        # Generate image
        with torch.autocast("cuda"):
            image = pipeline(
                prompt=prompt,
                height=height,
                width=width,
                num_inference_steps=num_inference_steps,
                guidance_scale=guidance_scale,
                callback=callback,
                callback_steps=1
            ).images[0]
    
    generation_time = time.time() - start_time
    console.print(f"[green]Image generated in {generation_time:.2f} seconds[/green]")
    
    return image


def main():
    parser = argparse.ArgumentParser(description="Generate images with fine-tuned FLUX model")
    parser.add_argument("--model_path", type=str, required=True,
                       help="Path to fine-tuned model (LoRA weights)")
    parser.add_argument("--base_model", type=str, default="black-forest-labs/FLUX.1-dev",
                       help="Base FLUX model name or path")
    parser.add_argument("--prompt", type=str, required=True,
                       help="Text prompt for image generation")
    parser.add_argument("--output", type=str, default="generated_image.png",
                       help="Output image path")
    parser.add_argument("--config", type=str, default=None,
                       help="Configuration file for generation parameters")
    parser.add_argument("--height", type=int, default=1024,
                       help="Image height")
    parser.add_argument("--width", type=int, default=1024,
                       help="Image width")
    parser.add_argument("--steps", type=int, default=50,
                       help="Number of inference steps")
    parser.add_argument("--guidance", type=float, default=7.5,
                       help="Guidance scale")
    parser.add_argument("--seed", type=int, default=None,
                       help="Random seed for reproducibility")
    parser.add_argument("--batch_prompts", type=str, default=None,
                       help="File containing multiple prompts (one per line)")
    parser.add_argument("--batch_output_dir", type=str, default="./generated_images",
                       help="Directory for batch generation outputs")
    
    args = parser.parse_args()
    
    # Setup logging
    setup_logging("INFO")
    logger = logging.getLogger(__name__)
    
    console.print("[bold blue]FLUX Fine-tuned Model Inference[/bold blue]")
    console.print("=" * 50)
    
    # Load configuration if provided
    generation_config = {
        'height': args.height,
        'width': args.width,
        'num_inference_steps': args.steps,
        'guidance_scale': args.guidance,
        'seed': args.seed
    }
    
    if args.config:
        try:
            config = load_config(args.config)
            generation_config.update(config.get('generation', {}))
        except Exception as e:
            console.print(f"[yellow]Warning: Could not load config file: {e}[/yellow]")
    
    # Check if model path exists
    if not os.path.exists(args.model_path):
        console.print(f"[red]Error: Model path {args.model_path} does not exist[/red]")
        return 1
    
    # Load model
    try:
        pipeline = load_fine_tuned_model(args.base_model, args.model_path)
        console.print("[green]Model loaded successfully![/green]")
        
    except Exception as e:
        console.print(f"[red]Error loading model: {e}[/red]")
        logger.error(f"Model loading failed: {e}")
        return 1
    
    # Batch generation mode
    if args.batch_prompts:
        console.print(f"[cyan]Batch generation mode: {args.batch_prompts}[/cyan]")
        
        try:
            with open(args.batch_prompts, 'r', encoding='utf-8') as f:
                prompts = [line.strip() for line in f if line.strip()]
            
            output_dir = Path(args.batch_output_dir)
            output_dir.mkdir(exist_ok=True)
            
            console.print(f"[green]Processing {len(prompts)} prompts...[/green]")
            
            for i, prompt in enumerate(prompts):
                try:
                    console.print(f"\n[bold]Prompt {i+1}/{len(prompts)}[/bold]")
                    
                    # Generate image
                    image = generate_image(pipeline, prompt, generation_config)
                    
                    # Save image
                    output_path = output_dir / f"image_{i+1:04d}.png"
                    image.save(output_path)
                    
                    # Save prompt
                    prompt_path = output_dir / f"prompt_{i+1:04d}.txt"
                    with open(prompt_path, 'w', encoding='utf-8') as f:
                        f.write(prompt)
                    
                    console.print(f"[green]Saved: {output_path}[/green]")
                    
                except Exception as e:
                    console.print(f"[red]Error generating image {i+1}: {e}[/red]")
                    continue
            
            console.print(f"\n[bold green]Batch generation completed! Images saved to {output_dir}[/bold green]")
            
        except Exception as e:
            console.print(f"[red]Error in batch generation: {e}[/red]")
            return 1
    
    # Single generation mode
    else:
        try:
            # Generate image
            image = generate_image(pipeline, args.prompt, generation_config)
            
            # Save image
            image.save(args.output)
            console.print(f"[green]Image saved to: {args.output}[/green]")
            
            # Save prompt
            prompt_file = Path(args.output).with_suffix('.txt')
            with open(prompt_file, 'w', encoding='utf-8') as f:
                f.write(args.prompt)
            
            console.print(f"[dim]Prompt saved to: {prompt_file}[/dim]")
            
        except Exception as e:
            console.print(f"[red]Error generating image: {e}[/red]")
            logger.error(f"Image generation failed: {e}")
            return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
