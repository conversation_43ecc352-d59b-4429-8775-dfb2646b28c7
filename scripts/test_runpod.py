#!/usr/bin/env python3
"""
Test script to verify RunPod A40 setup for FLUX QLoRA fine-tuning.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

import torch
import time
import subprocess
from pathlib import Path
from src.utils import get_system_info, calculate_memory_usage
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.progress import Progress

console = Console()


def test_runpod_environment():
    """Test RunPod environment detection."""
    console.print("[bold blue]Testing RunPod Environment[/bold blue]")
    
    runpod_indicators = {
        "Workspace Directory": os.path.exists('/workspace'),
        "Pod ID": bool(os.environ.get('RUNPOD_POD_ID')),
        "Pod Hostname": bool(os.environ.get('RUNPOD_POD_HOSTNAME')),
        "RunPod in Hostname": 'runpod' in os.environ.get('HOSTNAME', '').lower()
    }
    
    table = Table(title="RunPod Environment Check")
    table.add_column("Check", style="cyan")
    table.add_column("Status", style="white")
    table.add_column("Value", style="dim")
    
    is_runpod = False
    for check, status in runpod_indicators.items():
        status_icon = "✅" if status else "❌"
        value = ""
        
        if check == "Pod ID":
            value = os.environ.get('RUNPOD_POD_ID', 'Not set')
        elif check == "Pod Hostname":
            value = os.environ.get('RUNPOD_POD_HOSTNAME', 'Not set')
        elif check == "RunPod in Hostname":
            value = os.environ.get('HOSTNAME', 'Unknown')
        
        table.add_row(check, status_icon, value)
        if status:
            is_runpod = True
    
    console.print(table)
    return is_runpod


def test_a40_gpu():
    """Test A40 GPU availability and performance."""
    console.print("\n[bold blue]Testing A40 GPU[/bold blue]")
    
    if not torch.cuda.is_available():
        console.print("[red]❌ CUDA not available[/red]")
        return False
    
    gpu_info = []
    for i in range(torch.cuda.device_count()):
        props = torch.cuda.get_device_properties(i)
        gpu_info.append({
            "id": i,
            "name": props.name,
            "memory_gb": props.total_memory / 1024**3,
            "compute_capability": f"{props.major}.{props.minor}"
        })
    
    table = Table(title="GPU Information")
    table.add_column("GPU", style="cyan")
    table.add_column("Name", style="white")
    table.add_column("Memory (GB)", style="green")
    table.add_column("Compute", style="dim")
    table.add_column("A40 Compatible", style="yellow")
    
    a40_found = False
    for gpu in gpu_info:
        is_a40 = "A40" in gpu["name"]
        is_compatible = gpu["memory_gb"] >= 40  # At least 40GB for FLUX
        
        a40_status = "✅ A40" if is_a40 else ("✅ Compatible" if is_compatible else "❌ Too small")
        
        table.add_row(
            str(gpu["id"]),
            gpu["name"],
            f"{gpu['memory_gb']:.1f}",
            gpu["compute_capability"],
            a40_status
        )
        
        if is_a40 or is_compatible:
            a40_found = True
    
    console.print(table)
    return a40_found


def test_memory_performance():
    """Test memory allocation and performance."""
    console.print("\n[bold blue]Testing Memory Performance[/bold blue]")
    
    if not torch.cuda.is_available():
        console.print("[red]❌ CUDA not available for memory test[/red]")
        return False
    
    try:
        # Test memory allocation
        console.print("Testing memory allocation...")
        
        with Progress() as progress:
            task = progress.add_task("Allocating memory...", total=100)
            
            # Allocate memory in chunks to test capacity
            tensors = []
            chunk_size = 1024 * 1024 * 1024  # 1GB chunks
            max_chunks = 40  # Test up to 40GB
            
            for i in range(max_chunks):
                try:
                    tensor = torch.randn(chunk_size // 4, device='cuda')  # 4 bytes per float32
                    tensors.append(tensor)
                    progress.update(task, advance=100/max_chunks)
                    
                    memory_used = calculate_memory_usage()
                    if memory_used > 35:  # Stop before hitting limits
                        break
                        
                except torch.cuda.OutOfMemoryError:
                    break
            
            max_memory = calculate_memory_usage()
            
            # Clean up
            del tensors
            torch.cuda.empty_cache()
        
        console.print(f"[green]✅ Maximum allocatable memory: {max_memory:.1f} GB[/green]")
        
        # Test computation performance
        console.print("Testing computation performance...")
        
        start_time = time.time()
        x = torch.randn(4096, 4096, device='cuda')
        y = torch.randn(4096, 4096, device='cuda')
        
        for _ in range(100):
            z = torch.matmul(x, y)
        
        torch.cuda.synchronize()
        end_time = time.time()
        
        computation_time = end_time - start_time
        console.print(f"[green]✅ Matrix multiplication test: {computation_time:.2f} seconds[/green]")
        
        return max_memory >= 35  # Need at least 35GB for FLUX
        
    except Exception as e:
        console.print(f"[red]❌ Memory test failed: {e}[/red]")
        return False


def test_model_loading():
    """Test FLUX model loading capability."""
    console.print("\n[bold blue]Testing Model Loading[/bold blue]")
    
    try:
        # Test HuggingFace authentication
        hf_token = os.environ.get('HF_TOKEN')
        if not hf_token:
            console.print("[yellow]⚠️ HF_TOKEN not set - may not be able to access FLUX model[/yellow]")
        
        # Test basic imports
        console.print("Testing imports...")
        from transformers import AutoTokenizer
        from diffusers import FluxPipeline
        from peft import LoraConfig
        import bitsandbytes as bnb
        
        console.print("[green]✅ All required packages imported successfully[/green]")
        
        # Test tokenizer loading (lightweight test)
        console.print("Testing tokenizer loading...")
        tokenizer = AutoTokenizer.from_pretrained("openai/clip-vit-large-patch14")
        console.print("[green]✅ Tokenizer loaded successfully[/green]")
        
        return True
        
    except Exception as e:
        console.print(f"[red]❌ Model loading test failed: {e}[/red]")
        return False


def test_storage_setup():
    """Test storage configuration."""
    console.print("\n[bold blue]Testing Storage Setup[/bold blue]")
    
    required_dirs = [
        "/workspace/data/train",
        "/workspace/data/validation",
        "/workspace/outputs",
        "/workspace/logs",
        "/workspace/models"
    ]
    
    table = Table(title="Storage Directories")
    table.add_column("Directory", style="cyan")
    table.add_column("Status", style="white")
    table.add_column("Writable", style="green")
    
    all_good = True
    for directory in required_dirs:
        exists = os.path.exists(directory)
        writable = False
        
        if exists:
            try:
                test_file = os.path.join(directory, '.test_write')
                with open(test_file, 'w') as f:
                    f.write('test')
                os.remove(test_file)
                writable = True
            except:
                pass
        
        status = "✅ Exists" if exists else "❌ Missing"
        write_status = "✅ Yes" if writable else "❌ No"
        
        table.add_row(directory, status, write_status)
        
        if not exists or not writable:
            all_good = False
    
    console.print(table)
    
    # Test disk space
    try:
        result = subprocess.run(['df', '-h', '/workspace'], capture_output=True, text=True)
        if result.returncode == 0:
            console.print(f"\n[dim]Disk usage:\n{result.stdout}[/dim]")
    except:
        pass
    
    return all_good


def test_network_connectivity():
    """Test network connectivity for model downloads."""
    console.print("\n[bold blue]Testing Network Connectivity[/bold blue]")
    
    test_urls = [
        ("HuggingFace", "https://huggingface.co"),
        ("PyTorch Hub", "https://download.pytorch.org"),
        ("Weights & Biases", "https://wandb.ai")
    ]
    
    table = Table(title="Network Connectivity")
    table.add_column("Service", style="cyan")
    table.add_column("Status", style="white")
    table.add_column("Response Time", style="dim")
    
    all_connected = True
    for service, url in test_urls:
        try:
            import requests
            start_time = time.time()
            response = requests.get(url, timeout=10)
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                status = "✅ Connected"
                time_str = f"{response_time:.2f}s"
            else:
                status = f"❌ Error {response.status_code}"
                time_str = f"{response_time:.2f}s"
                all_connected = False
                
        except Exception as e:
            status = "❌ Failed"
            time_str = str(e)[:20] + "..."
            all_connected = False
        
        table.add_row(service, status, time_str)
    
    console.print(table)
    return all_connected


def main():
    """Main test function."""
    console.print(Panel.fit("[bold blue]RunPod A40 FLUX QLoRA Setup Test[/bold blue]", 
                           title="🧪 System Test"))
    
    tests = [
        ("RunPod Environment", test_runpod_environment),
        ("A40 GPU", test_a40_gpu),
        ("Memory Performance", test_memory_performance),
        ("Model Loading", test_model_loading),
        ("Storage Setup", test_storage_setup),
        ("Network Connectivity", test_network_connectivity)
    ]
    
    results = {}
    for test_name, test_func in tests:
        try:
            result = test_func()
            results[test_name] = result
        except Exception as e:
            console.print(f"[red]❌ {test_name} test crashed: {e}[/red]")
            results[test_name] = False
    
    # Summary
    console.print("\n" + "="*50)
    console.print("[bold blue]Test Summary[/bold blue]")
    
    summary_table = Table()
    summary_table.add_column("Test", style="cyan")
    summary_table.add_column("Result", style="white")
    
    all_passed = True
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        summary_table.add_row(test_name, status)
        if not result:
            all_passed = False
    
    console.print(summary_table)
    
    if all_passed:
        console.print("\n[bold green]🎉 All tests passed! RunPod A40 is ready for FLUX QLoRA training.[/bold green]")
        console.print("\n[green]Next steps:[/green]")
        console.print("1. Upload training images to /workspace/data/train/")
        console.print("2. Run: make prepare")
        console.print("3. Run: make train-runpod")
        return 0
    else:
        console.print("\n[bold red]❌ Some tests failed. Please fix the issues above.[/bold red]")
        console.print("\n[red]Common fixes:[/red]")
        console.print("• Run: make setup-runpod")
        console.print("• Set HF_TOKEN environment variable")
        console.print("• Check GPU drivers and CUDA installation")
        return 1


if __name__ == "__main__":
    exit(main())
