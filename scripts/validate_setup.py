#!/usr/bin/env python3
"""
Validation script to check if the setup is correct for FLUX fine-tuning.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

import torch
import subprocess
from pathlib import Path
from src.utils import get_system_info, estimate_memory_requirements, load_config
from rich.console import Console
from rich.table import Table
from rich.panel import Panel

console = Console()


def check_python_version():
    """Check Python version."""
    version = sys.version_info
    if version.major == 3 and version.minor >= 8:
        return True, f"Python {version.major}.{version.minor}.{version.micro}"
    else:
        return False, f"Python {version.major}.{version.minor}.{version.micro} (requires 3.8+)"


def check_cuda_installation():
    """Check CUDA installation."""
    try:
        result = subprocess.run(['nvcc', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            # Extract CUDA version from output
            for line in result.stdout.split('\n'):
                if 'release' in line:
                    cuda_version = line.split('release')[1].split(',')[0].strip()
                    return True, f"CUDA {cuda_version}"
        return False, "CUDA not found"
    except FileNotFoundError:
        return False, "nvcc not found"


def check_pytorch_cuda():
    """Check PyTorch CUDA support."""
    try:
        import torch
        if torch.cuda.is_available():
            return True, f"PyTorch {torch.__version__} with CUDA {torch.version.cuda}"
        else:
            return False, f"PyTorch {torch.__version__} without CUDA"
    except ImportError:
        return False, "PyTorch not installed"


def check_required_packages():
    """Check if all required packages are installed."""
    required_packages = {
        'torch': 'PyTorch',
        'transformers': 'Transformers',
        'diffusers': 'Diffusers', 
        'peft': 'PEFT',
        'bitsandbytes': 'BitsAndBytes',
        'accelerate': 'Accelerate',
        'datasets': 'Datasets',
        'PIL': 'Pillow',
        'cv2': 'OpenCV',
        'clip_interrogator': 'CLIP Interrogator',
        'wandb': 'Weights & Biases',
        'omegaconf': 'OmegaConf',
        'rich': 'Rich',
        'tqdm': 'TQDM'
    }
    
    results = {}
    for package, name in required_packages.items():
        try:
            if package == 'cv2':
                import cv2
                results[name] = (True, cv2.__version__)
            elif package == 'PIL':
                from PIL import Image
                results[name] = (True, "Available")
            else:
                module = __import__(package)
                version = getattr(module, '__version__', 'Unknown')
                results[name] = (True, version)
        except ImportError:
            results[name] = (False, "Not installed")
    
    return results


def check_model_access():
    """Check if we can access the FLUX model."""
    try:
        from huggingface_hub import HfApi
        api = HfApi()
        
        # Check if model exists and is accessible
        model_info = api.model_info("black-forest-labs/FLUX.1-dev")
        return True, "Model accessible"
    except Exception as e:
        return False, f"Cannot access model: {str(e)}"


def check_directories():
    """Check if required directories exist."""
    required_dirs = [
        "data/train",
        "data/validation", 
        "outputs",
        "logs",
        "config"
    ]
    
    results = {}
    for directory in required_dirs:
        path = Path(directory)
        if path.exists():
            results[directory] = (True, "Exists")
        else:
            results[directory] = (False, "Missing")
    
    return results


def check_config_file():
    """Check if configuration file is valid."""
    config_path = "config/training_config.yaml"
    try:
        if not os.path.exists(config_path):
            return False, "Config file missing"
        
        config = load_config(config_path)
        
        # Check required sections
        required_sections = ['model', 'training', 'dataset', 'qlora']
        missing_sections = [section for section in required_sections if section not in config]
        
        if missing_sections:
            return False, f"Missing sections: {', '.join(missing_sections)}"
        
        return True, "Valid configuration"
    except Exception as e:
        return False, f"Invalid config: {str(e)}"


def check_gpu_memory():
    """Check GPU memory requirements."""
    try:
        if not torch.cuda.is_available():
            return False, "No GPU available"
        
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
        
        # Load config to estimate requirements
        try:
            config = load_config("config/training_config.yaml")
            memory_est = estimate_memory_requirements(config)
            required_memory = memory_est['recommended_gpu_memory_gb']
            
            if gpu_memory >= required_memory:
                return True, f"{gpu_memory:.1f}GB available, {required_memory:.1f}GB required"
            else:
                return False, f"{gpu_memory:.1f}GB available, {required_memory:.1f}GB required"
        except:
            # Fallback estimation
            if gpu_memory >= 20:
                return True, f"{gpu_memory:.1f}GB available"
            else:
                return False, f"{gpu_memory:.1f}GB available (20GB+ recommended)"
    
    except Exception as e:
        return False, f"Error checking GPU: {str(e)}"


def main():
    """Main validation function."""
    console.print(Panel.fit("[bold blue]FLUX QLoRA Setup Validation[/bold blue]", 
                           title="🔍 System Check"))
    
    # System checks
    checks = [
        ("Python Version", check_python_version),
        ("CUDA Installation", check_cuda_installation), 
        ("PyTorch CUDA", check_pytorch_cuda),
        ("FLUX Model Access", check_model_access),
        ("Configuration File", check_config_file),
        ("GPU Memory", check_gpu_memory)
    ]
    
    # Create results table
    results_table = Table(title="System Validation Results")
    results_table.add_column("Check", style="cyan")
    results_table.add_column("Status", style="white")
    results_table.add_column("Details", style="dim")
    
    all_passed = True
    
    for check_name, check_func in checks:
        try:
            passed, details = check_func()
            status = "✅ PASS" if passed else "❌ FAIL"
            results_table.add_row(check_name, status, details)
            if not passed:
                all_passed = False
        except Exception as e:
            results_table.add_row(check_name, "❌ ERROR", str(e))
            all_passed = False
    
    console.print(results_table)
    console.print()
    
    # Package checks
    package_results = check_required_packages()
    
    package_table = Table(title="Package Installation Status")
    package_table.add_column("Package", style="cyan")
    package_table.add_column("Status", style="white") 
    package_table.add_column("Version", style="dim")
    
    for package, (installed, version) in package_results.items():
        status = "✅ Installed" if installed else "❌ Missing"
        package_table.add_row(package, status, version)
        if not installed:
            all_passed = False
    
    console.print(package_table)
    console.print()
    
    # Directory checks
    dir_results = check_directories()
    
    dir_table = Table(title="Directory Structure")
    dir_table.add_column("Directory", style="cyan")
    dir_table.add_column("Status", style="white")
    dir_table.add_column("Notes", style="dim")
    
    for directory, (exists, notes) in dir_results.items():
        status = "✅ Exists" if exists else "❌ Missing"
        dir_table.add_row(directory, status, notes)
        if not exists and directory in ["config"]:  # Only fail for critical directories
            all_passed = False
    
    console.print(dir_table)
    console.print()
    
    # System information
    system_info = get_system_info()
    
    system_table = Table(title="System Information")
    system_table.add_column("Component", style="cyan")
    system_table.add_column("Details", style="white")
    
    system_table.add_row("CPU Cores", str(system_info['cpu_count']))
    system_table.add_row("Total Memory", f"{system_info['memory_total_gb']:.1f} GB")
    system_table.add_row("Available Memory", f"{system_info['memory_available_gb']:.1f} GB")
    
    if 'gpus' in system_info:
        for i, gpu in enumerate(system_info['gpus']):
            system_table.add_row(f"GPU {i}", f"{gpu['name']} ({gpu['memory_total_gb']:.1f} GB)")
    
    console.print(system_table)
    console.print()
    
    # Final result
    if all_passed:
        console.print(Panel.fit("[bold green]✅ All checks passed! System is ready for FLUX fine-tuning.[/bold green]", 
                               title="🎉 Validation Complete"))
        console.print("\n[green]Next steps:[/green]")
        console.print("1. Place training images in data/train/")
        console.print("2. Run: python scripts/prepare_dataset.py --data_dir data/train")
        console.print("3. Run: python scripts/train.py")
        return 0
    else:
        console.print(Panel.fit("[bold red]❌ Some checks failed. Please fix the issues above.[/bold red]", 
                               title="⚠️ Validation Failed"))
        console.print("\n[red]Common fixes:[/red]")
        console.print("• Install missing packages: pip install -r requirements.txt")
        console.print("• Create missing directories: python setup.py")
        console.print("• Check CUDA installation and GPU drivers")
        return 1


if __name__ == "__main__":
    exit(main())
