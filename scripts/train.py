#!/usr/bin/env python3
"""
Main training script for FLUX QLoRA fine-tuning.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

import argparse
import logging
import torch
from pathlib import Path
from src.flux_qlora_trainer import FluxQLoRATrainer
from src.dataset_processor import DatasetProcessor
from src.data_loader import create_dataloader
from src.utils import (
    load_config, setup_logging, validate_config, 
    estimate_memory_requirements, create_directory_structure,
    get_system_info, check_disk_space
)
from rich.console import Console
from rich.table import Table
from rich.panel import Panel

console = Console()


def main():
    parser = argparse.ArgumentParser(description="Train FLUX model with QLoRA")
    parser.add_argument("--config", type=str, default="config/training_config.yaml",
                       help="Path to configuration file")
    parser.add_argument("--resume_from", type=str, default=None,
                       help="Path to checkpoint to resume from")
    parser.add_argument("--train_data_dir", type=str, default=None,
                       help="Override training data directory")
    parser.add_argument("--val_data_dir", type=str, default=None,
                       help="Override validation data directory")
    parser.add_argument("--output_dir", type=str, default=None,
                       help="Override output directory")
    parser.add_argument("--log_level", type=str, default="INFO",
                       choices=["DEBUG", "INFO", "WARNING", "ERROR"])
    parser.add_argument("--dry_run", action="store_true",
                       help="Perform dry run without actual training")
    
    args = parser.parse_args()
    
    # Setup logging
    setup_logging(args.log_level, "logs/training.log")
    logger = logging.getLogger(__name__)
    
    console.print(Panel.fit("[bold blue]FLUX QLoRA Fine-tuning[/bold blue]", 
                           title="🚀 Training Script"))
    
    # Load and validate configuration
    try:
        config = load_config(args.config)
        logger.info(f"Loaded configuration from {args.config}")
        
        # Apply command line overrides
        if args.train_data_dir:
            config.setdefault('dataset', {})['train_data_dir'] = args.train_data_dir
        if args.val_data_dir:
            config.setdefault('dataset', {})['validation_data_dir'] = args.val_data_dir
        if args.output_dir:
            config.setdefault('training', {})['output_dir'] = args.output_dir
            
    except Exception as e:
        console.print(f"[red]Error loading configuration: {e}[/red]")
        return 1
    
    # Validate configuration
    if not validate_config(config):
        console.print("[red]Configuration validation failed[/red]")
        return 1
    
    # Display system information
    system_info = get_system_info()
    
    info_table = Table(title="System Information")
    info_table.add_column("Component", style="cyan")
    info_table.add_column("Details", style="white")
    
    info_table.add_row("PyTorch Version", system_info.get('pytorch_version', 'Unknown'))
    info_table.add_row("CUDA Version", system_info.get('cuda_version', 'N/A'))
    info_table.add_row("CPU Cores", str(system_info['cpu_count']))
    info_table.add_row("Total Memory", f"{system_info['memory_total_gb']:.1f} GB")
    
    if 'gpus' in system_info:
        for i, gpu in enumerate(system_info['gpus']):
            info_table.add_row(f"GPU {i}", 
                             f"{gpu['name']} ({gpu['memory_total_gb']:.1f} GB, {gpu['memory_free_gb']:.1f} GB free)")
    
    console.print(info_table)
    console.print()
    
    # Estimate memory requirements
    memory_est = estimate_memory_requirements(config)
    
    memory_table = Table(title="Memory Requirements Estimation")
    memory_table.add_column("Component", style="cyan")
    memory_table.add_column("Memory (GB)", style="white")
    
    memory_table.add_row("Base Model", f"{memory_est['base_model_gb']:.1f}")
    memory_table.add_row("LoRA Parameters", f"{memory_est['lora_parameters_gb']:.1f}")
    memory_table.add_row("Batch Data", f"{memory_est['batch_data_gb']:.1f}")
    memory_table.add_row("Optimizer States", f"{memory_est['optimizer_states_gb']:.1f}")
    memory_table.add_row("[bold]Total Estimated[/bold]", f"[bold]{memory_est['total_estimated_gb']:.1f}[/bold]")
    memory_table.add_row("[bold green]Recommended GPU[/bold green]", 
                        f"[bold green]{memory_est['recommended_gpu_memory_gb']:.1f}[/bold green]")
    
    console.print(memory_table)
    
    # Check if we have enough GPU memory
    if 'gpus' in system_info and system_info['gpus']:
        available_memory = max(gpu['memory_free_gb'] for gpu in system_info['gpus'])
        if available_memory < memory_est['recommended_gpu_memory_gb']:
            console.print(f"[yellow]Warning: Available GPU memory ({available_memory:.1f} GB) "
                         f"may be insufficient for optimal training[/yellow]")
    
    # Check disk space
    output_dir = config.get('training', {}).get('output_dir', './outputs')
    if not check_disk_space(output_dir, 20.0):  # Require 20GB free space
        console.print("[red]Insufficient disk space for training outputs[/red]")
        return 1
    
    # Create directory structure
    create_directory_structure('.')
    
    if args.dry_run:
        console.print("[yellow]Dry run completed. Configuration and system checks passed.[/yellow]")
        return 0
    
    # Load and prepare datasets
    console.print("\n[green]Loading datasets...[/green]")
    
    try:
        # Load training data
        train_data_dir = config['dataset']['train_data_dir']
        processor = DatasetProcessor(config)
        train_dataset_items = processor.process_directory(train_data_dir)
        
        if not train_dataset_items:
            console.print("[red]No training data found[/red]")
            return 1
        
        console.print(f"Loaded {len(train_dataset_items)} training images")
        
        # Load validation data if specified
        val_dataset_items = None
        val_data_dir = config.get('dataset', {}).get('validation_data_dir')
        if val_data_dir and os.path.exists(val_data_dir):
            val_dataset_items = processor.process_directory(val_data_dir)
            console.print(f"Loaded {len(val_dataset_items)} validation images")
        
    except Exception as e:
        console.print(f"[red]Error loading datasets: {e}[/red]")
        logger.error(f"Dataset loading failed: {e}")
        return 1
    
    # Initialize trainer
    console.print("\n[green]Initializing trainer...[/green]")
    
    try:
        trainer = FluxQLoRATrainer(config)
        trainer.setup_model()
        
    except Exception as e:
        console.print(f"[red]Error initializing trainer: {e}[/red]")
        logger.error(f"Trainer initialization failed: {e}")
        return 1
    
    # Create data loaders
    console.print("[green]Creating data loaders...[/green]")
    
    try:
        train_dataloader = create_dataloader(
            train_dataset_items, 
            config, 
            trainer.tokenizer, 
            shuffle=True
        )
        
        val_dataloader = None
        if val_dataset_items:
            val_dataloader = create_dataloader(
                val_dataset_items,
                config,
                trainer.tokenizer,
                shuffle=False
            )
        
        console.print(f"Training batches: {len(train_dataloader)}")
        if val_dataloader:
            console.print(f"Validation batches: {len(val_dataloader)}")
        
    except Exception as e:
        console.print(f"[red]Error creating data loaders: {e}[/red]")
        logger.error(f"Data loader creation failed: {e}")
        return 1
    
    # Resume from checkpoint if specified
    if args.resume_from:
        console.print(f"[yellow]Resuming from checkpoint: {args.resume_from}[/yellow]")
        try:
            # Load checkpoint logic would go here
            pass
        except Exception as e:
            console.print(f"[red]Error loading checkpoint: {e}[/red]")
            return 1
    
    # Start training
    console.print("\n[bold green]Starting training...[/bold green]")
    
    try:
        trainer.train(train_dataloader, val_dataloader)
        console.print("\n[bold green]Training completed successfully![/bold green]")
        
    except KeyboardInterrupt:
        console.print("\n[yellow]Training interrupted by user[/yellow]")
        logger.info("Training interrupted by user")
        return 1
        
    except Exception as e:
        console.print(f"\n[red]Training failed: {e}[/red]")
        logger.error(f"Training failed: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
