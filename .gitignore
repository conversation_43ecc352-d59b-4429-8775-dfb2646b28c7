# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyTorch
*.pth
*.pt
*.bin
*.safetensors

# Jupyter Notebook
.ipynb_checkpoints

# Environment
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Project specific
models/
outputs/
logs/*.log
checkpoints/
generated_images/*.png
generated_images/*.jpg
data/train/*.jpg
data/train/*.png
data/train/*.jpeg
data/train/*.webp
data/validation/*.jpg
data/validation/*.png
data/validation/*.jpeg
data/validation/*.webp
*.csv
*.json

# Weights & Biases
wandb/
.wandb/

# Temporary files
*.tmp
*.temp
sample_image.*
backup_*.tar.gz

# Large files (use Git LFS)
*.ckpt
*.model
